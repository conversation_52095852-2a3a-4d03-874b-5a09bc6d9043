/**
 * TestingEngine 统一导出
 *
 * 使用示例:
 * ```typescript
 * import { TestingEngine } from '@automation-testing/core/testing-engine';
 *
 * const engine = new TestingEngine({
 *   executorType: 'web',
 *   headless: true,
 *   timeout: 30000,
 * });
 *
 * // 执行 YAML 文件
 * const result = await engine.runYamlFile('test.yml');
 *
 * // 执行单步
 * const stepResult = await engine.executeStep({
 *   action: 'click',
 *   role: 'button',
 *   roleOptions: { name: '登录' }
 * });
 *
 * // 清理资源
 * await engine.cleanup();
 * ```
 */

// 主要类
export { TestingEngine } from './testing-engine';

// 服务层
export { TestService } from './test-service';
export type { ITestService, ServiceStatus } from './test-service';
export { ExecutionEngine } from './execution-engine';
export type { ExecutionContext } from './execution-engine';
export { APIService } from './api-service';
export { TaskManager } from './task-manager';
export { ResourceService } from '../infrastructure/resource-service';
export type { TestTask, TaskStatus, TestProgress } from './task-manager';

// 辅助类
export { ConfigService } from './config-service';
export { DataAccessor } from './data-accessor';

// 类型定义
export type {
  EngineOptions,
  TestOptions,
  StepOptions,
  StepData,
  TestResult,
  StepResult,
  ResolvedConfig,
  ExecutorType,
  ConnectionType,
  SystemStatus,
} from './types';

// 默认导出（方便使用）
import { TestingEngine } from './testing-engine';
export default TestingEngine;
