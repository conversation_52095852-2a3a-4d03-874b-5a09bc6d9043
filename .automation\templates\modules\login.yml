config:
  name: "蓝奥无人奶茶车调度系统 - 登录功能完整测试"
  description: "包含正常登录、记住密码、登录失败、退出登录等完整场景的测试套件"
  baseUrl: "http://************:9999"
  executor: "element-plus"
  testMode: "flow"
  timeout: 30000
  captureScreenshots: true
  continueOnFailure: false
  variables:
    validUser:
      username: "admin"
      password: "666666"
    invalidUser:
      username: "wronguser"
      password: "wrongpass"
    testUser:
      username: "testuser"
      password: "test123"

# 测试数据源
# dataSources:
#   - name: "loginTestData"
#     type: "static"
#     data:
#       validUser:
#         username: "admin"
#         password: "123456"
#       invalidUser:
#         username: "wronguser"
#         password: "wrongpass"
#       testUser:
#         username: "testuser"
#         password: "test123"

# 钩子函数
hooks:
  beforeAll:
    - action: executeScript
      script: |
        // 清除所有可能的用户数据，确保测试环境干净
        console.log('123123')
      description: "清理测试环境"

tests:
  # 测试1：正常登录流程
  - name: "正常登录测试"
    description: "使用有效凭据进行正常登录测试"
    steps:
      - action: useScript
        script: complex-login
        parameters:
          username: "{{validUser.username}}"
          password: "{{validUser.password}}"
          
      - action: useTemplate
        template: "complete-login-flow-template"
        parameters:
          baseUrl: "{{config.baseUrl}}"
          username: "{{validUser.username}}"
          password: "{{validUser.password}}"
          rememberPassword: false
          verifySuccess: true

  # 测试2：记住密码功能测试
  # - name: "记住密码功能测试"
  #   description: "测试勾选记住密码选项的登录流程"
  #   steps:
  #     - action: useTemplate
  #       template: "complete-login-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         username: "{{validUser.username}}"
  #         password: "{{validUser.password}}"
  #         rememberPassword: true
  #         verifySuccess: true
      
  #     # 验证localStorage中是否保存了用户信息
      # - action: executeScript
      #   script: |
      #     const username = localStorage.getItem('username');
      #     const password = localStorage.getItem('password');
      #     if (!username || !password) {
      #       throw new Error('记住密码功能未正常工作：localStorage中未找到用户凭据');
      #     }
      #     return { username, password };
      #   description: "验证记住密码功能是否正常工作"

  # 测试3：登录失败测试
  # - name: "登录失败测试"
  #   description: "使用无效凭据测试登录失败场景"
  #   steps:
  #     - action: useTemplate
  #       template: "login-navigation-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
      
  #     - action: useTemplate
  #       template: "login-template"
  #       parameters:
  #         username: "{{invalidUser.username}}"
  #         password: "{{invalidUser.password}}"
  #         rememberPassword: false
      
  #     # 验证登录失败消息
  #     - action: verify
  #       type: text
  #       data: "账号或密码错误"
  #       assertion: visible
  #       description: "验证登录失败错误消息显示"

  # 测试4：空字段验证测试
  # - name: "空字段验证测试"
  #   description: "测试必填字段的验证功能"
  #   steps:
  #     - action: useTemplate
  #       template: "login-navigation-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
      
  #     # 尝试不填写任何信息直接登录
  #     - action: click
  #       role: button
  #       roleOptions:
  #         name: "登录"
  #       description: "不填写信息直接点击登录按钮"
      
  #     # 验证必填字段提示
  #     - action: verify
  #       type: text
  #       data: "请输入账号"
  #       assertion: visible
  #       description: "验证账号必填提示"

  # 测试5：退出登录测试
  # - name: "退出登录测试"
  #   description: "测试完整的登录后退出流程"
  #   steps:
  #     # 先进行正常登录
  #     - action: useTemplate
  #       template: "complete-login-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         username: "{{validUser.username}}"
  #         password: "{{validUser.password}}"
  #         rememberPassword: false
  #         verifySuccess: true
      
  #     # 等待登录完成
  #     - action: wait
  #       value: 3000
  #       description: "等待登录完成并页面稳定"
      
  #     # 执行退出登录
  #     - action: useTemplate
  #       template: "complete-logout-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         verifyLogout: true

  # 测试6：登录状态持久化测试
  # - name: "登录状态持久化测试"
  #   description: "测试页面刷新后登录状态是否保持"
  #   steps:
  #     # 先进行正常登录
  #     - action: useTemplate
  #       template: "complete-login-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         username: "{{validUser.username}}"
  #         password: "{{validUser.password}}"
  #         rememberPassword: true
  #         verifySuccess: true
      
  #     # 等待登录完成
  #     - action: wait
  #       value: 2000
  #       description: "等待登录完成"
      
  #     # 刷新页面
  #     - action: reload
  #       description: "刷新页面测试登录状态持久化"
      
  #     # 等待页面加载
  #     - action: wait
  #       value: 3000
  #       description: "等待页面重新加载"
      
  #     # 验证是否仍保持登录状态（不应该回到登录页面）
  #     - action: executeScript
  #       script: |
  #         const currentUrl = window.location.href;
  #         if (currentUrl.includes('/login')) {
  #           throw new Error('登录状态未持久化：页面刷新后回到了登录页面');
  #         }
  #         return { currentUrl };
  #       description: "验证登录状态是否持久化"

  # 测试7：多次登录失败测试
  # - name: "多次登录失败测试"
  #   description: "测试连续多次登录失败的处理"
  #   steps:
  #     - action: useTemplate
  #       template: "login-navigation-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
      
  #     # 循环进行多次登录失败尝试
  #     - action: forEach
  #       items: [1, 2, 3]
  #       steps:
  #         - action: fill
  #           role: textbox
  #           roleOptions:
  #             name: "*账号"
  #           value: "{{invalidUser.username}}"
          
  #         - action: fill
  #           role: textbox
  #           roleOptions:
  #             name: "*密码"
  #           value: "{{invalidUser.password}}"
          
  #         - action: click
  #           role: button
  #           roleOptions:
  #             name: "登录"
          
  #         - action: wait
  #           value: 1000
  #           description: "等待登录处理"
          
  #         # 清空输入框准备下次尝试
  #         - action: fill
  #           role: textbox
  #           roleOptions:
  #             name: "*账号"
  #           value: ""
          
  #         - action: fill
  #           role: textbox
  #           roleOptions:
  #             name: "*密码"
  #           value: ""

  # 测试8：登录后立即退出测试
  # - name: "登录后立即退出测试"
  #   description: "测试登录成功后立即退出的场景"
  #   steps:
  #     # 执行完整的登录流程
  #     - action: useTemplate
  #       template: "complete-login-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         username: "{{validUser.username}}"
  #         password: "{{validUser.password}}"
  #         rememberPassword: false
  #         verifySuccess: true
      
  #     # 立即执行退出登录
  #     - action: useTemplate
  #       template: "complete-logout-flow-template"
  #       parameters:
  #         baseUrl: "http://localhost:9999"
  #         verifyLogout: true
      
  #     # 验证确实回到了登录页面
  #     - action: verify
  #       type: text
  #       data: "欢迎登录"
  #       assertion: visible
  #       description: "确认已成功退出并返回登录页面"
