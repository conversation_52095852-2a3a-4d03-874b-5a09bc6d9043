import type { Page } from '@playwright/test';
import { logger } from '../utils/logger';
import type { TaskManager } from './task-manager';
import type { TestingEngine } from './testing-engine';
import type { TestStep, ExecutorType, StepOptions, PageEx } from '../types';
import type { PageContext } from '../types/page-api';
import { prepareScriptForEvaluate } from '../utils/helpers';
import { glob } from 'glob'
import path from 'path'
import fs from 'fs'
import { YAMLSchemaExporter } from '../schema/yaml-schema-exporter';


/**
 * API注入服务
 * 专门负责页面API注入，分离关注点
 * 重构后使用 TestingEngine 获得完整的系统支持
 */
export class APIService {
  private injectedPages: Set<string> = new Set();
  private testingEngine: TestingEngine;
  private taskManager: TaskManager;

  constructor(testingEngine: TestingEngine | null, taskManager: TaskManager) {
    this.testingEngine = testingEngine as TestingEngine;
    this.taskManager = taskManager;
    logger.debug('APIService 初始化完成 (使用 TestingEngine)');
  }

  /**
   * 设置 TestingEngine (延迟注入)
   * 解决循环依赖问题
   */
  setTestingEngine(testingEngine: TestingEngine): void {
    this.testingEngine = testingEngine;
    logger.debug('APIService TestingEngine 注入完成');
  }

  /**
   * 为页面注入自动化测试API
   */
  async injectPageAPI(page: Page, pageId: string): Promise<void> {
    try {
      // 检查页面是否已经注入过API
      if (this.injectedPages.has(pageId)) {
        logger.debug('页面已注入API，跳过', { url: pageId });
        return;
      }

      // 检查页面是否可访问
      if (page.isClosed()) {
        logger.debug('页面已关闭，跳过注入', { url: pageId });
        return;
      }

      // 检查API是否已存在
      const hasAPI = await page
        .evaluate(() => {
          // @ts-ignore
          return typeof (window as any).automationTesting === 'object';
        })
        .catch(() => false);

      if (hasAPI) {
        logger.debug('页面已有API，标记为已注入', { url: pageId });
        this.injectedPages.add(pageId);
        return;
      }

      logger.info('开始为页面注入自动化测试API', { url: pageId });

      // 注入核心执行API
      await this.injectExecutionAPIs(page, pageId);
      
      // 注入任务管理API
      await this.injectTaskManagementAPIs(page, pageId);
      
      // 注入工具API
      await this.injectUtilityAPIs(page);

      // 创建统一的API对象
      await this.createUnifiedAPIObject(page);

      this.injectedPages.add(pageId);
      logger.info('页面API注入完成', { url: pageId });

    } catch (error) {
      logger.error('页面API注入失败', {
        url: pageId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * 注入执行相关API
   */
  private async injectExecutionAPIs(page: Page, pageId: string): Promise<void> {
    // 创建页面上下文
    const createPageContext = (): PageContext => ({
      page,
      pageId,
      url: page.url()
    });

    // 主要测试执行API
    await page.exposeFunction(
      'executeYamlTest',
      async (yamlContent: string, options: any = {}) => {
        const task = this.taskManager.createTask(yamlContent, pageId);

        // 添加页面上下文到选项中
        const optionsWithPageContext = {
          ...options,
          pageContext: createPageContext()
        };

        logger.debug('页面调用YAML测试执行', {
          pageId,
          pageUrl: page.url(),
          hasPageContext: true
        });

        return this.testingEngine.runYamlContent(yamlContent, optionsWithPageContext, pageId);
      }
    );

    // 单步执行API
    await page.exposeFunction(
      'executeStepActionTest',
      async (stepData: TestStep, options: {executorType?: ExecutorType} = {}) => {
        // 添加页面上下文到选项中
        const optionsWithPageContext: StepOptions = {
          ...options,
          pageContext: createPageContext() // 传递页面上下文
        };

        logger.debug('页面调用单步执行', {
          action: stepData.action,
          pageId,
          pageUrl: page.url(),
          hasPageContext: true
        });

        return this.testingEngine.executeStep(stepData, optionsWithPageContext);
      }
    );

    // 模板执行API（支持双模式：templateId 或 YAML内容）
    await page.exposeFunction(
      'executeTemplateTest',
      async (templateIdOrYaml: string, parameters: { [key: string]: any } = {}, options: { mode?: 'auto' | 'id' | 'yaml' } = {}) => {
        // 添加页面上下文到选项中
        const optionsWithPageContext: StepOptions = {
          pageContext: createPageContext()
        };

        // 检测模式
        const mode = options.mode || this.detectTemplateMode(templateIdOrYaml);

        logger.debug('页面调用模板执行', {
          templateIdOrYaml: mode === 'yaml' ? '<YAML内容>' : templateIdOrYaml,
          mode,
          parameters,
          pageId,
          pageUrl: page.url(),
          hasPageContext: true
        });

        // YAML模式：执行动态模板
        return this.executeYamlTemplate(templateIdOrYaml, parameters, optionsWithPageContext);
      }
    );
  }

  /**
   * 注入任务管理API
   */
  private async injectTaskManagementAPIs(page: Page, pageId: string): Promise<void> {
    // 获取测试结果
    await page.exposeFunction(
      'getTestResult',
      (taskId: string) => this.taskManager.getTestResult(taskId)
    );

    // 取消测试
    await page.exposeFunction(
      'cancelTest',
      (taskId: string) => this.taskManager.cancelTest(taskId)
    );

    // 获取运行中的测试
    await page.exposeFunction(
      'getRunningTests',
      () => this.taskManager.getRunningTests()
    );

    // 简化的进度和历史API（向后兼容）
    await page.exposeFunction(
      'getTestProgress',
      (taskId: string) => this.taskManager.getTestProgress(taskId)
    );

    await page.exposeFunction(
      'getTestHistory',
      (limit?: number) => this.taskManager.getTestHistory(limit)
    );
  }

  /**
   * 注入工具API
   */
  private async injectUtilityAPIs(page: Page): Promise<void> {
    // YAML验证
    await page.exposeFunction(
      'validateYaml',
      async (yamlContent: string) => {
        const { DSLParser } = await import('../dsl/parser');
        const result = DSLParser.parseWithValidation(yamlContent);
        return {
          isValid: result.isValid,
          errors: result.errors,
          warnings: result.warnings,
        };
      }
    );

    // 获取YAML Schema
    await page.exposeFunction(
      'getYAMLSchema',
      async () => {
        return await YAMLSchemaExporter.exportForLLMAsync();
      }
    );

    // 获取页面snaoshot
    await page.exposeFunction(
      'getSnapshotForAI',
      async () => {
        return await (page as PageEx)._snapshotForAI();
      }
    );

    // 获取页面源代码
    await page.exposeFunction(
      'getSourceCodeFile',
      async (filepath: string, wholeModule: boolean = false) => {
        try {
          // 解析目标路径
          const targetPath = path.resolve(filepath);
          
          // 检查是否是单个文件请求
          if (!wholeModule) {
            if (fs.existsSync(targetPath) && /\.(vue|ts|tsx)$/.test(targetPath)) {
              const content = fs.readFileSync(targetPath, 'utf-8');
              
              // 如果是Vue文件，过滤掉style部分
              if (targetPath.endsWith('.vue')) {
                const vueContent = content.replace(/<style(\s[^>]*)?>[\s\S]*?<\/style>/gi, '');
                return {
                  filepath: targetPath,
                  content: vueContent,
                };
              }
              
              return {
                filepath: targetPath,
                content,
              };
            }
            return { error: '文件不存在或格式不支持' };
          }

          // 处理wholeModule模式
          const pathParts = filepath.split(/[/\\]/);
          const viewsIndex = pathParts.findIndex(part => part === 'views');
          
          if (viewsIndex === -1) {
            return { error: '路径中未找到views目录' };
          }

          const modulePath = pathParts.slice(viewsIndex + 1, -1).join('/');
          const searchPattern = `**/views/${modulePath}/**/*.{vue,ts,tsx}`;

          console.log('cwd: ', pathParts.slice(0, viewsIndex).join('\\'))
          
          const files = await glob(searchPattern, { 
            cwd: pathParts.slice(0, viewsIndex).join('\\\\'),
            absolute: true 
          });

          const results = [];
          for (const file of files) {
            const content = fs.readFileSync(file, 'utf-8');
            
            // 过滤Vue文件的style部分
            let processedContent = content;
            if (file.endsWith('.vue')) {
              processedContent = content.replace(/<style(\s[^>]*)?>[\s\S]*?<\/style>/gi, '');
            }
            
            results.push({
              filepath: file,
              content: processedContent,
            });
          }

          return results;
        } catch (error) {
          return { 
            error: `读取文件失败: ${error instanceof Error ? error.message : String(error)}` 
          };
        }
      }
    );

    // 执行脚本
    await page.exposeFunction(
      'executeScript',
      async (script: string, context?: Record<string, any>) => {
        const wrappedScript = prepareScriptForEvaluate(script, context);
        return await page.evaluate(wrappedScript);
      }
    );
  } 

  /**
   * 创建统一的API对象
   */
  private async createUnifiedAPIObject(page: Page): Promise<void> {
    await page.addInitScript(() => {
      // 创建automationTesting API对象
      (window as any).automationTesting = {
        // 执行API
        executeYamlTest: (window as any).executeYamlTest,
        executeStepActionTest: (window as any).executeStepActionTest,
        executeTemplateTest: (window as any).executeTemplateTest,
        
        // 任务管理API
        getTestResult: (window as any).getTestResult,
        getTestProgress: (window as any).getTestProgress,
        cancelTest: (window as any).cancelTest,
        getRunningTests: (window as any).getRunningTests,
        getTestHistory: (window as any).getTestHistory,
        
        // 工具API
        validateYaml: (window as any).validateYaml,
        getYAMLSchema: (window as any).getYAMLSchema,
        getSnapshotForAI: (window as any).getSnapshotForAI,
        getSourceCodeFile: (window as any).getSourceCodeFile,
        executeScript: (window as any).executeScript,
      };

      // 清理全局函数，只保留automationTesting对象
      const functionsToClean = [
        'executeYamlTest', 'executeStepActionTest', 'executeTemplateTest',
        'getTestResult', 'getTestProgress', 'cancelTest', 'getRunningTests',
        'getTestHistory', 'validateYaml', 'getYAMLSchema', 'getSnapshotForAI',
        'getSourceCodeFile', 'executeScript' 
      ];

      functionsToClean.forEach(funcName => {
        try {
          delete (window as any)[funcName];
        } catch (e) {
          // 忽略删除错误
        }
      });
    });
  }

  /**
   * 检测模板模式（ID 或 YAML）
   */
  private detectTemplateMode(content: string): 'id' | 'yaml' {
    // 启发式检测：包含换行符且包含YAML关键字的认为是YAML内容
    if (content.includes('\n') &&
        (content.includes('template:') ||
         content.includes('templates:') ||
         content.includes('steps:') ||
         content.includes('name:'))) {
      return 'yaml';
    }
    return 'id';
  }

  /**
   * 执行YAML模板内容
   */
  private async executeYamlTemplate(
    yamlContent: string,
    parameters: { [key: string]: any },
    options: StepOptions
  ): Promise<any> {
    try {
      // 解析YAML内容，提取模板定义
      const parsedYaml = this.parseYamlTemplates(yamlContent);

      // 根据parameters创建useTemplate步骤
      const useTemplateSteps = this.createUseTemplateSteps(parameters);

      if (useTemplateSteps.length === 0) {
        throw new Error('没有找到要执行的模板，请检查parameters参数');
      }

      // 创建临时测试套件YAML，包含模板定义和useTemplate步骤
      const tempTestSuite = this.buildTestSuiteWithTemplates(parsedYaml, useTemplateSteps);

      logger.debug('执行模板测试套件', {
        templatesCount: Object.keys(parsedYaml.templates || {}).length,
        useTemplateStepsCount: useTemplateSteps.length
      });

      // 执行测试套件
      return this.testingEngine.runYamlContent(tempTestSuite, options);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('YAML模板执行失败', { error: errorMessage });

      return {
        success: false,
        error: errorMessage,
        duration: 0
      };
    }
  }

  /**
   * 解析YAML内容，提取模板定义
   */
  private parseYamlTemplates(yamlContent: string): any {
    try {
      const yaml = require('yaml');
      const parsed = yaml.parse(yamlContent);

      if (!parsed) {
        throw new Error('YAML内容为空');
      }

      // 验证是否包含模板定义
      if (!parsed.templates && !parsed.template) {
        throw new Error('YAML内容中未找到模板定义');
      }

      return parsed;
    } catch (error) {
      throw new Error(`解析YAML失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 根据parameters创建useTemplate步骤
   */
  private createUseTemplateSteps(parameters: { [key: string]: any }): any[] {
    const steps: any[] = [];

    // parameters的每个key代表一个模板名称，value是该模板的参数
    for (const [templateName, templateParams] of Object.entries(parameters)) {
      steps.push({
        action: 'useTemplate',
        template: templateName,
        parameters: templateParams,
        description: `执行模板: ${templateName}`
      });
    }

    return steps;
  }

  /**
   * 构建包含模板定义和useTemplate步骤的测试套件
   */
  private buildTestSuiteWithTemplates(parsedYaml: any, useTemplateSteps: any[]): string {
    const yaml = require('yaml');

    // 构建完整的测试套件对象
    const testSuite = {
      config: {
        name: "临时模板测试",
        testMode: "flow",
        executorType: "element-plus",
        continueOnFailure: false  // 确保失败时立即中断
      },
      // 包含原始模板定义
      templates: parsedYaml.templates || {},
      // 创建测试用例，使用useTemplate步骤
      tests: [
        {
          name: "执行模板测试",
          description: "通过useTemplate动作执行指定的模板",
          steps: useTemplateSteps
        }
      ]
    };

    // 转换为YAML字符串
    return yaml.stringify(testSuite);
  }

  /**
   * 清理API注入记录
   */
  cleanup(): void {
    this.injectedPages.clear();
    logger.debug('APIService 清理完成');
  }
}
