import * as fs from 'node:fs';
import { logger } from '../utils/logger';
import { ConfigService } from './config-service';
import { TestService } from './test-service';
import type {
  EngineOptions,
  TestOptions,
  StepOptions,
  StepData,
  TestResult,
  StepResult,
} from './types';

// 注意：AutomationTestingSystem已被删除，功能已集成到TestingEngine中

/**
 * TestingEngine - 统一测试引擎
 * 极简的 4 方法接口，内部使用 TestService 统一业务逻辑
 */
export class TestingEngine {
  private configService: ConfigService;
  private testService: TestService;
  private isInitialized = false;

  constructor(options?: EngineOptions) {
    this.configService = new ConfigService(options);
    this.testService = new TestService(this.configService);

    logger.info('TestingEngine 创建完成', {
      options: this.configService.getBaseConfig()
    });
  }
  
  /**
   * 执行 YAML 测试文件
   * 替代：runTests() 全局函数
   * 支持完整的 hooks、数据源、模板功能
   */
  async runYamlFile(filePath: string, options?: TestOptions): Promise<TestResult> {
    await this.ensureInitialized();

    logger.info('开始执行 YAML 文件', { filePath, options });

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error(`测试文件不存在: ${filePath}`);
      }

      // 读取 YAML 内容
      const yamlContent = fs.readFileSync(filePath, 'utf-8');
      return await this.runYamlContent(yamlContent, options, filePath);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('YAML 文件执行失败', { filePath, error: errorMessage });

      return {
        success: false,
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          skipped: 0,
          duration: 0,
        },
        results: [],
        errors: [errorMessage],
      };
    }
  }

  /**
   * 执行 YAML 内容
   * 使用 TestService 统一处理，支持完整的 hooks 和生命周期管理
   */
  async runYamlContent(yamlContent: string, options?: TestOptions, testName?: string): Promise<TestResult> {
    await this.ensureInitialized();

    try {
      // 使用 TestService 执行，统一处理 hooks
      const result = await this.testService.executeYamlContent(yamlContent, options);

      logger.info('YAML 内容执行完成', {
        testName,
        success: result.success,
        duration: result.summary.duration
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('YAML 内容执行失败', { error: errorMessage });

      return {
        success: false,
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          skipped: 0,
          duration: 0,
        },
        results: [],
        errors: [errorMessage],
      };
    }
  }
  
  /**
   * 执行单个测试步骤
   * 使用 TestService 统一处理页面调用和后台调用
   */
  async executeStep(stepData: StepData, options?: StepOptions): Promise<StepResult> {
    await this.ensureInitialized();

    logger.info('开始执行单步测试（使用 TestService）', { stepData, options });

    try {
      // 使用 TestService 执行，统一处理逻辑
      const result = await this.testService.executeStep(stepData, options);

      logger.info('单步测试执行完成', {
        success: result.success,
        duration: result.duration,
        stepAction: stepData.action
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('单步测试执行失败', {
        error: errorMessage,
        stepAction: stepData.action
      });

      return {
        success: false,
        duration: 0,
        error: errorMessage,
      };
    }
  }

  /**
   * 执行模板测试
   * 使用 TestService 统一处理模板执行
   */
  async executeTemplate(
    templateId: string,
    parameters: Record<string, any> = {},
    options?: StepOptions
  ): Promise<StepResult> {
    await this.ensureInitialized();

    logger.info('开始执行模板测试（使用 TestService）', { templateId, parameters, options });

    try {
      // 使用 TestService 执行模板
      const result = await this.testService.executeTemplate(templateId, parameters);

      logger.info('模板测试执行完成', {
        success: result.success,
        duration: result.duration,
        templateId
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('模板测试执行失败', {
        error: errorMessage,
        templateId
      });

      return {
        success: false,
        duration: 0,
        error: errorMessage,
      };
    }
  }
  
  /**
   * 清理资源
   * 使用 TestService 统一清理资源
   */
  async cleanup(options?: { keepBrowser?: boolean }): Promise<void> {
    logger.info('开始清理 TestingEngine 资源（使用 TestService）', options);

    try {
      // 使用 TestService 清理
      await this.testService.cleanup(options);

      // AutomationSystem已被删除，功能已集成到TestService中

      // 重置状态
      if (!options?.keepBrowser) {
        this.isInitialized = false;
      }

      logger.info('TestingEngine 资源清理完成');

    } catch (error) {
      logger.warn('TestingEngine 清理过程中出现错误', {
        error: error instanceof Error ? error.message : String(error),
      });

      // 即使出错也重置状态
      if (!options?.keepBrowser) {
        this.isInitialized = false;
      }
    }
  }

  /**
   * 重新初始化系统
   * 用于配置更改后的重新初始化
   */
  async reinitialize(newOptions?: Partial<EngineOptions>): Promise<void> {
    logger.info('重新初始化 TestingEngine', { newOptions });

    // 更新配置
    if (newOptions) {
      const currentConfig = this.configService.getBaseConfig();
      this.configService = new ConfigService({ ...currentConfig, ...newOptions });
    }

    // 强制重新初始化
    await this.ensureInitialized({ force: true });
  }

  /**
   * 检查系统健康状态
   */
  async healthCheck(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      // 检查初始化状态
      if (!this.isInitialized) {
        issues.push('系统未初始化');
      }

      // 检查TestService（替代原AutomationSystem）
      if (!this.testService) {
        issues.push('测试服务未创建');
      } else {
        // 检查TestService状态
        const status = this.testService.getStatus();
        if (!status.initialized) {
          issues.push('测试服务未初始化');
        }
        if (status.resourceManager !== 'ready') {
          issues.push('资源管理器未就绪');
        }
        if (status.taskManager !== 'ready') {
          issues.push('任务管理器未就绪');
        }
      }

      return {
        healthy: issues.length === 0,
        issues
      };

    } catch (error) {
      issues.push(`健康检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return {
        healthy: false,
        issues
      };
    }
  }

  // 清理了不再使用的方法，现在所有逻辑都通过 TestService 统一处理

  /**
   * 懒加载初始化
   * 使用 TestService 统一初始化
   */
   async ensureInitialized(options?: { force?: boolean }): Promise<void> {
    // 如果已初始化且不强制重新初始化，则直接返回
    if (this.isInitialized && !options?.force) {
      return;
    }

    // 如果强制重新初始化，先清理现有资源
    if (this.isInitialized && options?.force) {
      await this.cleanup();
    }

    logger.info('开始初始化 TestingEngine（使用 TestService）');

    try {
      // 使用 TestService 初始化
      await this.testService.initialize();

      // 注入 TestingEngine 到 APIService (解决循环依赖)
      this.testService.setTestingEngineToAPIService(this);

      this.isInitialized = true;

      logger.info('TestingEngine 初始化完成', {
        serviceStatus: this.testService.getStatus()
      });

    } catch (error) {
      this.isInitialized = false;

      logger.error('TestingEngine 初始化失败', {
        error: error instanceof Error ? error.message : String(error),
      });

      throw new Error(`TestingEngine 初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
