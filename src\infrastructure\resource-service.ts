import { logger } from '../utils/logger';
import { chromium, firefox, webkit } from '@playwright/test';
import type { <PERSON><PERSON><PERSON>, Page, BrowserContext } from '@playwright/test';
import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs';



/**
 * 浏览器连接配置
 */
export interface BrowserConnectionConfig {
  /** 连接类型：CDP连接现有浏览器 或 启动新浏览器 */
  connectionType: 'cdp' | 'new-browser';
  /** 浏览器类型 */
  browser: 'chromium';
  /** 是否无头模式 */
  headless: boolean;
  /** 慢动作延迟（毫秒） */
  slowMo?: number;
  /** CDP调试端口（提供则尝试连接现有浏览器，否则启动新浏览器） */
  debuggingPort?: number;
  /** CDP端点URL */
  cdpEndpoint?: string;
  /** 额外的浏览器启动参数 */
  args?: string[];
}

/**
 * 资源状态
 */
export interface ResourceStatus {
  /** 是否已初始化 */
  initialized: boolean;
  /** 浏览器连接状态 */
  browserStatus: 'connected' | 'disconnected' | 'error';
  /** 活跃页面数量 */
  activePages: number;
  /** 连接类型 */
  connectionType: 'cdp' | 'new-browser' | 'unknown';
  /** 是否拥有浏览器实例（用于清理判断） */
  ownsBrowser: boolean;
}

/**
 * 页面创建选项
 */
export interface PageCreateOptions {
  /** 初始URL */
  url?: string;
  /** 视口大小 */
  viewport?: { width: number; height: number };
  /** 用户代理 */
  userAgent?: string;
  /** 是否启用JavaScript */
  javaScriptEnabled?: boolean;
}

/**
 * 资源清理选项
 */
export interface ResourceCleanupOptions {
  /** 是否保留浏览器实例 */
  keepBrowser?: boolean;
  /** 是否强制清理 */
  force?: boolean;
  /** 清理超时时间（毫秒） */
  timeout?: number;
}

/**
 * ResourceService 接口
 * 统一的资源管理服务，负责浏览器和页面的生命周期管理
 */
export interface IResourceService {
  /**
   * 初始化资源服务
   * 根据配置自动选择最佳的连接方式
   */
  initialize(config: BrowserConnectionConfig): Promise<void>;
  
  /**
   * 获取浏览器实例
   * 如果未初始化则抛出错误
   */
  getBrowser(): Browser;
  
  /**
   * 获取浏览器上下文
   * 如果未初始化则抛出错误
   */
  getBrowserContext(): BrowserContext;
  
  /**
   * 获取默认页面
   * 如果不存在则创建一个新页面
   */
  getPage(): Promise<Page>;
  
  /**
   * 创建新页面
   * 支持自定义页面选项
   */
  createPage(options?: PageCreateOptions): Promise<Page>;
  
  /**
   * 关闭指定页面
   * 如果是最后一个页面，会创建一个新的默认页面
   */
  closePage(page: Page): Promise<void>;
  
  /**
   * 获取所有活跃页面
   */
  getActivePages(): Page[];
  
  /**
   * 获取资源状态
   */
  getStatus(): ResourceStatus;
  
  /**
   * 检查服务是否就绪
   */
  isReady(): boolean;

  /**
   * 设置APIService（新架构支持）
   */
  setAPIService(apiService: any): Promise<void>;

  /**
   * 轻量级清理
   * 关闭页面但保留浏览器连接
   */
  cleanupKeepBrowser(): Promise<void>;
  
  /**
   * 完全清理
   * 关闭所有资源包括浏览器实例
   */
  cleanup(options?: ResourceCleanupOptions): Promise<void>;
}

/**
 * ResourceService 实现类
 * 统一管理浏览器和页面资源，支持CDP和新浏览器两种连接方式
 */
export class ResourceService implements IResourceService {
  private browser: Browser | null = null;
  private browserContext: BrowserContext | null = null;
  private defaultPage: Page | null = null;
  private config: BrowserConnectionConfig | null = null;
  private isInitialized = false;
  private ownsBrowser = false; // 是否拥有浏览器实例（用于清理判断）

  // 新架构组件支持
  private apiService: any | null = null;
  private pageEventListeners: Map<string, any[]> = new Map(); // 页面事件监听器

  constructor() {
    logger.debug('ResourceService 创建完成');
  }
  
  /**
   * 初始化资源服务
   * 根据配置自动选择最佳的连接方式
   */
  async initialize(config: BrowserConnectionConfig): Promise<void> {
    if (this.isInitialized) {
      logger.warn('ResourceService 已经初始化，跳过重复初始化');
      return;
    }
    
    this.config = { ...config };
    
    logger.info('开始初始化 ResourceService', {
      connectionType: config.connectionType,
      browser: config.browser,
      headless: config.headless
    });
    
    try {
      if (config.connectionType === 'cdp') {
        await this.initializeWithCDP();
      } else {
        await this.initializeWithNewBrowser();
      }
      
      // 创建默认页面
      await this.ensureDefaultPage();
      
      this.isInitialized = true;
      
      logger.info('ResourceService 初始化完成', {
        connectionType: config.connectionType,
        browserConnected: !!this.browser,
        defaultPageReady: !!this.defaultPage
      });
      
    } catch (error) {
      logger.error('ResourceService 初始化失败', {
        error: error instanceof Error ? error.message : String(error),
        config: config
      });
      
      // 清理失败的初始化
      await this.cleanup({ force: true });
      throw error;
    }
  }
  
  /**
   * 获取浏览器实例
   */
  getBrowser(): Browser {
    if (!this.browser) {
      throw new Error('浏览器未初始化，请先调用 initialize()');
    }
    return this.browser;
  }
  
  /**
   * 获取浏览器上下文
   */
  getBrowserContext(): BrowserContext {
    if (!this.browserContext) {
      throw new Error('浏览器上下文未初始化，请先调用 initialize()');
    }
    return this.browserContext;
  }
  
  /**
   * 获取默认页面
   */
  async getPage(): Promise<Page> {
    if (!this.defaultPage || this.defaultPage.isClosed()) {
      await this.ensureDefaultPage();
    }
    return this.defaultPage!;
  }
  
  /**
   * 创建新页面
   */
  async createPage(options?: PageCreateOptions): Promise<Page> {
    const context = this.getBrowserContext();

    // 创建新页面
    const page = await context.newPage();

    // 设置页面选项
    if (options?.viewport) {
      await page.setViewportSize(options.viewport);
    }
    if (options?.userAgent) {
      await page.setExtraHTTPHeaders({ 'User-Agent': options.userAgent });
    }
    // 注意：JavaScript启用状态需要在上下文级别设置，这里跳过
    if (options?.javaScriptEnabled !== undefined) {
      logger.debug('JavaScript启用状态需要在上下文级别设置，页面级别跳过');
    }

    if (options?.url) {
      await page.goto(options.url);
    }

    logger.debug('新页面创建完成', {
      url: options?.url || 'about:blank',
      viewport: options?.viewport
    });

    return page;
  }
  
  /**
   * 关闭指定页面
   */
  async closePage(page: Page): Promise<void> {
    if (page.isClosed()) {
      return;
    }
    
    const isDefaultPage = page === this.defaultPage;
    
    await page.close();
    
    // 如果关闭的是默认页面，需要创建新的默认页面
    if (isDefaultPage) {
      this.defaultPage = null;
      await this.ensureDefaultPage();
    }
    
    logger.debug('页面关闭完成', { isDefaultPage });
  }
  
  /**
   * 获取所有活跃页面
   */
  getActivePages(): Page[] {
    if (!this.browserContext) {
      return [];
    }
    
    return this.browserContext.pages().filter(page => !page.isClosed());
  }
  
  /**
   * 获取资源状态
   */
  getStatus(): ResourceStatus {
    return {
      initialized: this.isInitialized,
      browserStatus: this.browser ? 'connected' : 'disconnected',
      activePages: this.getActivePages().length,
      connectionType: this.config?.connectionType || 'unknown',
      ownsBrowser: this.ownsBrowser,
    };
  }
  
  /**
   * 检查服务是否就绪
   */
  isReady(): boolean {
    return this.isInitialized && !!this.browser && !!this.browserContext;
  }

  /**
   * 设置APIService（新架构支持）
   */
  async setAPIService(apiService: any): Promise<void> {
    this.apiService = apiService;
    logger.debug('APIService已设置到ResourceService');

    // 如果已初始化，启动页面事件监听
    if (this.isInitialized) {
      await this.setupPageEventListeners();
      logger.info('页面事件监听已启动，支持自动API注入');
    }
  }

  /**
   * 设置页面事件监听，支持自动API注入
   */
  private async setupPageEventListeners(): Promise<void> {
    if (!this.browser || !this.apiService) {
      logger.debug('浏览器或APIService未准备好，跳过页面事件监听设置');
      return;
    }

    logger.info('设置页面事件监听器');

    try {
      // 为所有现有页面注入API
      await this.injectToAllExistingPages();

      // 监听浏览器新页面创建事件
      this.setupBrowserPageListeners();

      // 监听当前页面的导航和加载事件
      await this.setupCurrentPageListeners();

      logger.info('页面事件监听器设置完成');

    } catch (error) {
      logger.error('设置页面事件监听器失败', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * 为所有现有页面注入API
   */
  private async injectToAllExistingPages(): Promise<void> {
    if (!this.browser || !this.apiService) return;

    try {
      const contexts = this.browser.contexts();
      let injectedCount = 0;

      for (const context of contexts) {
        const pages = context.pages();
        for (const page of pages) {
          try {
            await this.apiService.injectPageAPI(page, page.url());
            injectedCount++;
          } catch (error) {
            logger.warn('为现有页面注入API失败', {
              url: page.url(),
              error: error instanceof Error ? error.message : String(error),
            });
          }
        }
      }

      logger.info(`已为 ${injectedCount} 个现有页面注入API`);
    } catch (error) {
      logger.error('为现有页面注入API时发生错误', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * 设置浏览器级别的页面监听器
   */
  private setupBrowserPageListeners(): void {
    if (!this.browser || !this.browserContext || !this.apiService) return;

    // 监听当前上下文的新页面创建
    const pageListener = async (page: any) => {
      try {
        logger.info('检测到新页面创建', { url: page.url() });

        // 等待页面加载完成后注入API
        await page.waitForLoadState('domcontentloaded', { timeout: 5000 });
        await this.apiService.injectPageAPI(page, page.url());

        logger.info('新页面API注入完成', { url: page.url() });
      } catch (error) {
        logger.warn('为新页面注入API失败', {
          url: page.url(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
    };

    this.browserContext.on('page', pageListener);

    // 记录监听器以便清理
    const contextId = 'browser-context';
    if (!this.pageEventListeners.has(contextId)) {
      this.pageEventListeners.set(contextId, []);
    }
    this.pageEventListeners.get(contextId)!.push({ event: 'page', listener: pageListener });

    logger.debug('浏览器页面监听器设置完成');
  }

  /**
   * 设置当前页面的事件监听器
   * 简化事件监听，避免重复注入API
   */
  private async setupCurrentPageListeners(): Promise<void> {
    if (!this.defaultPage || !this.apiService) return;

    const page = this.defaultPage;
    const pageId = page.url() || 'default';

    // 防抖机制：避免短时间内重复注入
    let lastInjectionTime = 0;
    const INJECTION_DEBOUNCE_MS = 1000; // 1秒内不重复注入

    const injectAPIWithDebounce = async (url: string, eventType: string) => {
      const now = Date.now();
      if (now - lastInjectionTime < INJECTION_DEBOUNCE_MS) {
        logger.debug(`跳过重复API注入 (${eventType})`, { url, timeSinceLastInjection: now - lastInjectionTime });
        return;
      }

      try {
        logger.info(`${eventType}，注入API`, { url });
        await this.apiService.injectPageAPI(page, url);
        lastInjectionTime = now;
      } catch (error) {
        logger.error(`${eventType}后注入API失败`, {
          url,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    };

    // 只监听页面导航事件（最关键的事件）
    // framenavigated 在页面URL变化时触发，是最可靠的重新注入时机
    const frameNavigatedListener = async (frame: any) => {
      if (frame === page.mainFrame()) {
        await injectAPIWithDebounce(frame.url(), '页面导航完成');
      }
    };

    // 监听DOM内容加载完成事件作为备用
    // 仅在API缺失时才重新注入，避免不必要的重复注入
    const domContentLoadedListener = async () => {
      try {
        const url = page.url();
        logger.debug('DOM内容加载完成，检查API状态', { url });

        // 检查API是否存在
        const hasAPI = await page.evaluate(() => {
          return typeof (window as any).automationTesting === 'object';
        }).catch(() => false);

        if (!hasAPI) {
          await injectAPIWithDebounce(url, 'API缺失，重新注入');
        } else {
          logger.debug('API已存在，无需重新注入', { url });
        }
      } catch (error) {
        logger.error('DOM加载后检查API失败', {
          url: page.url(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
    };

    // 添加事件监听器（移除load事件，避免重复）
    page.on('framenavigated', frameNavigatedListener);
    page.on('domcontentloaded', domContentLoadedListener);

    // 记录监听器以便清理
    if (!this.pageEventListeners.has(pageId)) {
      this.pageEventListeners.set(pageId, []);
    }
    const listeners = this.pageEventListeners.get(pageId)!;
    listeners.push(
      { event: 'framenavigated', listener: frameNavigatedListener },
      { event: 'domcontentloaded', listener: domContentLoadedListener }
    );

    logger.debug('页面事件监听器设置完成（已优化，避免重复注入）');
  }

  /**
   * 清理指定页面的事件监听器
   */
  private cleanupPageEventListeners(pageId: string): void {
    const listeners = this.pageEventListeners.get(pageId);
    if (listeners) {
      logger.debug('清理页面事件监听器', { pageId, listenerCount: listeners.length });

      // 这里不需要实际移除监听器，因为页面关闭或导航时会自动清理
      // 只需要从记录中移除即可
      this.pageEventListeners.delete(pageId);

      logger.debug('页面事件监听器清理完成', { pageId });
    }
  }

  /**
   * 轻量级清理
   * 保持浏览器运行，避免关闭最后一个页面导致浏览器退出
   */
  async cleanupKeepBrowser(): Promise<void> {
    logger.info('开始轻量级清理 ResourceService（保持浏览器运行）');

    try {
      const pages = this.getActivePages();
      logger.debug('当前活跃页面数量', { pageCount: pages.length });

      if (pages.length === 0) {
        logger.info('没有活跃页面，无需清理');
        return;
      }

      if (pages.length === 1) {
        // 只有一个页面时，导航到空白页而不是关闭，避免浏览器退出
        const page = pages[0];
        if (!page.isClosed()) {
          logger.info('只有一个页面，导航到空白页以保持浏览器运行');

          // 清理页面事件监听器
          this.cleanupPageEventListeners(page.url() || 'default');

          // 导航到空白页
          await page.goto('about:blank', { waitUntil: 'domcontentloaded', timeout: 5000 });

          // 保持页面引用，但标记为已清理
          this.defaultPage = page;

          logger.info('页面已重置为空白页，浏览器保持运行');
        }
      } else {
        // 多个页面时，关闭除最后一个页面外的所有页面
        logger.info('多个页面，关闭多余页面，保留一个页面');

        for (let i = 0; i < pages.length - 1; i++) {
          const page = pages[i];
          if (!page.isClosed()) {
            // 清理页面事件监听器
            this.cleanupPageEventListeners(page.url() || `page-${i}`);
            await page.close();
          }
        }

        // 保留最后一个页面并导航到空白页
        const lastPage = pages[pages.length - 1];
        if (!lastPage.isClosed()) {
          // 清理页面事件监听器
          this.cleanupPageEventListeners(lastPage.url() || 'last-page');

          // 导航到空白页
          await lastPage.goto('about:blank', { waitUntil: 'domcontentloaded', timeout: 5000 });

          // 保持页面引用
          this.defaultPage = lastPage;

          logger.info('保留最后一个页面并重置为空白页');
        }
      }

      logger.info('轻量级清理完成，浏览器保持运行', {
        browserConnected: !!this.browser,
        hasDefaultPage: !!this.defaultPage,
        defaultPageUrl: this.defaultPage?.url(),
        note: '浏览器实例未关闭，保留空白页面以防止浏览器退出'
      });

    } catch (error) {
      logger.warn('轻量级清理过程中出现错误', {
        error: error instanceof Error ? error.message : String(error),
      });

      // 如果清理失败，尝试确保至少有一个页面存在
      try {
        if (!this.defaultPage || this.defaultPage.isClosed()) {
          logger.info('清理失败后尝试创建新的空白页面');
          this.defaultPage = await this.createPage();
          if (this.defaultPage) {
            await this.defaultPage.goto('about:blank');
          }
        }
      } catch (fallbackError) {
        logger.error('创建备用页面失败', {
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError),
        });
      }
    }
  }
  
  /**
   * 完全清理
   */
  async cleanup(options: ResourceCleanupOptions = {}): Promise<void> {
    logger.info('开始清理 ResourceService 资源', options);
    
    try {
      // 如果只是保留浏览器，执行轻量级清理
      if (options.keepBrowser && !options.force) {
        await this.cleanupKeepBrowser();
        return;
      }
      
      // 关闭所有页面
      if (this.browserContext) {
        const pages = this.browserContext.pages();
        for (const page of pages) {
          if (!page.isClosed()) {
            await page.close();
          }
        }
      }
      
      // 关闭浏览器（仅当我们拥有浏览器实例时）
      if (this.browser && this.ownsBrowser) {
        await this.browser.close();
        logger.debug('浏览器实例已关闭');
      } else if (this.browser && !this.ownsBrowser) {
        logger.debug('浏览器实例由外部管理，跳过关闭');
      }
      
      // 重置所有状态
      this.browser = null;
      this.browserContext = null;
      this.defaultPage = null;
      this.config = null;
      this.isInitialized = false;
      this.ownsBrowser = false;
      
      logger.info('ResourceService 资源清理完成');
      
    } catch (error) {
      logger.warn('ResourceService 清理过程中出现错误', {
        error: error instanceof Error ? error.message : String(error),
      });
      
      // 即使出错也重置状态
      this.browser = null;
      this.browserContext = null;
      this.defaultPage = null;
      this.isInitialized = false;
      this.ownsBrowser = false;
    }
  }

  // === 私有方法 ===

  /**
   * 检测本地Chrome可执行文件路径
   */
  private detectLocalChromePath(): string | null {
    const possiblePaths = [
      // Windows
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
      // macOS
      '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    ];

    for (const chromePath of possiblePaths) {
      try {
        if (fs.existsSync(chromePath)) {
          logger.debug('找到本地Chrome路径', { path: chromePath });
          return chromePath;
        }
      } catch (error) {
        // 忽略检测错误，继续下一个路径
      }
    }

    logger.debug('未找到本地Chrome，将使用Playwright内置Chromium');
    return null;
  }



  /**
   * 使用CDP连接初始化
   */
  private async initializeWithCDP(): Promise<void> {

    const cdpEndpoint = this.config!.cdpEndpoint ||
      `http://localhost:${this.config!.debuggingPort || 9222}`;

    logger.debug('尝试CDP连接', { cdpEndpoint });

    try {
      // 连接到现有浏览器实例
      this.browser = await chromium.connectOverCDP(cdpEndpoint);
      this.ownsBrowser = false; // CDP连接不拥有浏览器实例

      // 获取或创建浏览器上下文
      const contexts = this.browser.contexts();
      if (contexts.length > 0) {
        this.browserContext = contexts[0];
        logger.debug('使用现有浏览器上下文');
      } else {
        this.browserContext = await this.browser.newContext();
        logger.debug('创建新浏览器上下文');
      }

      logger.info('CDP连接成功', {
        endpoint: cdpEndpoint,
        contexts: contexts.length
      });

    } catch (error) {
      logger.error('CDP连接失败', {
        endpoint: cdpEndpoint,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error(`CDP连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 使用新浏览器初始化
   * 统一使用本地Chrome的launchPersistentContext，如果找不到则回退到Chromium
   */
  private async initializeWithNewBrowser(): Promise<void> {

    logger.debug('启动新浏览器实例', {
      browser: this.config!.browser,
      headless: this.config!.headless
    });

    try {
      // 对于Chromium，优先使用本地Chrome的持久化上下文
      if (this.config!.browser === 'chromium') {
        const localChromePath = this.detectLocalChromePath();

        if (localChromePath) {
          // 使用本地Chrome的持久化上下文
          await this.launchWithLocalChrome(localChromePath);
          return;
        } else {
          logger.info('未找到本地Chrome，使用Playwright内置Chromium');
        }
      }

      // 回退到标准浏览器启动
      const browserType = chromium;

      const launchOptions: any = {
        headless: this.config!.headless,
        slowMo: this.config!.slowMo,
        args: this.config!.args,
      };

      // 启动浏览器实例
      this.browser = await browserType.launch(launchOptions);
      this.ownsBrowser = true;

      // 创建浏览器上下文
      this.browserContext = await this.browser.newContext();

      logger.info('浏览器实例启动成功', {
        browser: this.config!.browser,
        headless: this.config!.headless,
        usingLocalChrome: false
      });

    } catch (error) {
      logger.error('浏览器启动失败', {
        browser: this.config!.browser,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error(`浏览器启动失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 使用本地Chrome启动持久化上下文
   */
  private async launchWithLocalChrome(executablePath: string): Promise<void> {
    logger.debug('使用本地Chrome启动持久化上下文', {
      executablePath,
      headless: this.config!.headless,
      debuggingPort: this.config!.debuggingPort
    });

    try {
      // 获取用户数据目录（使用时间戳确保唯一性）
      const userDataDir = path.join(os.tmpdir(), `playwright-chrome`);

      // 启动持久化上下文
      const launchOptions = {
        executablePath,
        headless: this.config!.headless,
        slowMo: this.config!.slowMo,
        debuggingPort: this.config!.debuggingPort || 9222,
        bypassCSP: true, // 默认启用
        handleSIGHUP: false,
        handleSIGINT: false,
        handleSIGTERM: false,
        args: this.config!.args || [
          `--remote-debugging-port=${this.config!.debuggingPort || 9222}`,
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--disable-web-security',
          '--disable-extensions',
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-setuid-sandbox'
        ]
      };

      this.browserContext = await chromium.launchPersistentContext(userDataDir, launchOptions);
      this.browser = this.browserContext.browser()!;
      this.ownsBrowser = true;

      logger.info('本地Chrome持久化上下文启动成功', {
        executablePath,
        userDataDir,
        debuggingPort: this.config!.debuggingPort || 9222,
        headless: this.config!.headless
      });

    } catch (error) {
      logger.error('本地Chrome启动失败', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error(`本地Chrome启动失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 确保默认页面存在
   */
  private async ensureDefaultPage(): Promise<void> {
    if (this.defaultPage && !this.defaultPage.isClosed()) {
      return;
    }

    const context = this.getBrowserContext();

    // 检查是否已有页面可以复用
    const existingPages = context.pages().filter(page => !page.isClosed());
    if (existingPages.length > 0) {
      this.defaultPage = existingPages[0];
      logger.debug('复用现有页面作为默认页面');
      return;
    }

    // 创建新的默认页面
    this.defaultPage = await context.newPage();
    logger.debug('创建新的默认页面');
  }
}

/**
 * ResourceService 工厂类
 * 提供简单的创建接口，自动处理连接方式选择和回退逻辑
 */
export class ResourceServiceFactory {
  /**
   * 创建ResourceService实例
   * 自动选择最佳的连接方式，支持回退机制
   */
  static async create(config: BrowserConnectionConfig): Promise<IResourceService> {
    const service = new ResourceService();

    logger.info('ResourceServiceFactory 开始创建服务', {
      connectionType: config.connectionType,
      browser: config.browser
    });

    try {
      await service.initialize(config);
      return service;

    } catch (error) {
      logger.warn('ResourceService 初始化失败，尝试回退策略', {
        originalConnectionType: config.connectionType,
        error: error instanceof Error ? error.message : String(error),
      });

      // 简化回退策略：CDP失败回退到新浏览器
      if (config.connectionType === 'cdp') {
        const fallbackConfig: BrowserConnectionConfig = {
          ...config,
          connectionType: 'new-browser',
        };

        logger.info('尝试回退到新浏览器模式', fallbackConfig);

        const fallbackService = new ResourceService();
        await fallbackService.initialize(fallbackConfig);

        logger.info('回退策略成功，使用新浏览器模式');
        return fallbackService;
      }

      // 如果是新浏览器模式失败，则抛出原始错误
      throw error;
    }
  }

  /**
   * 创建CDP连接的ResourceService
   * 专门用于连接现有浏览器实例
   */
  static async createWithCDP(
    debuggingPort: number = 9222,
    options: Partial<BrowserConnectionConfig> = {}
  ): Promise<IResourceService> {
    const config: BrowserConnectionConfig = {
      connectionType: 'cdp',
      browser: 'chromium',
      headless: false,
      debuggingPort,
      ...options,
    };

    return await ResourceServiceFactory.create(config);
  }

  /**
   * 创建新浏览器的ResourceService
   * 专门用于启动新的浏览器实例
   */
  static async createWithNewBrowser(
    options: Partial<BrowserConnectionConfig> = {}
  ): Promise<IResourceService> {
    const config: BrowserConnectionConfig = {
      connectionType: 'new-browser',
      browser: 'chromium',
      headless: true,
      ...options,
    };

    return await ResourceServiceFactory.create(config);
  }

  /**
   * 智能创建ResourceService
   * 根据是否提供debuggingPort自动选择连接方式：
   * - 提供debuggingPort：尝试CDP连接现有浏览器
   * - 未提供debuggingPort：启动新浏览器（优先本地Chrome）
   */
  static async createSmart(
    options: Partial<BrowserConnectionConfig> = {}
  ): Promise<IResourceService> {
    // 根据是否提供debuggingPort自动选择连接类型
    const connectionType = options.debuggingPort ? 'cdp' : 'new-browser';

    const config: BrowserConnectionConfig = {
      connectionType,
      browser: 'chromium',
      headless: false,
      debuggingPort: options.debuggingPort || 9222,
      ...options,
    };

    return await ResourceServiceFactory.create(config);
  }
}

/**
 * 便捷的创建函数
 */
export const createResourceService = ResourceServiceFactory.create;
export const createResourceServiceWithCDP = ResourceServiceFactory.createWithCDP;
export const createResourceServiceWithNewBrowser = ResourceServiceFactory.createWithNewBrowser;
export const createResourceServiceSmart = ResourceServiceFactory.createSmart;
