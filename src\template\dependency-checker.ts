import { logger } from '../utils/logger';
import type { TemplateDefinition } from './template-registry';

/**
 * 依赖关系信息
 */
export interface DependencyInfo {
  templateId: string;
  dependencies: string[];
  dependents: string[];
}

/**
 * 循环依赖检测结果
 */
export interface CircularDependencyResult {
  hasCircularDependency: boolean;
  cycles: string[][];
  dependencyGraph: Map<string, string[]>;
}

/**
 * 模板依赖检测器
 * 负责检测和防止模板间的循环依赖
 */
export class DependencyChecker {
  private dependencyGraph = new Map<string, string[]>();
  private reverseGraph = new Map<string, string[]>(); // 反向依赖图
  private visiting = new Set<string>();
  private visited = new Set<string>();

  /**
   * 构建依赖图
   * @param templates 模板列表
   */
  buildDependencyGraph(templates: TemplateDefinition[]): void {
    this.dependencyGraph.clear();
    this.reverseGraph.clear();

    // 初始化所有模板节点
    for (const template of templates) {
      this.dependencyGraph.set(template.id, []);
      this.reverseGraph.set(template.id, []);
    }

    // 构建依赖关系
    for (const template of templates) {
      const dependencies = this.extractDependencies(template);
      this.dependencyGraph.set(template.id, dependencies);

      // 构建反向依赖图
      for (const dep of dependencies) {
        if (!this.reverseGraph.has(dep)) {
          this.reverseGraph.set(dep, []);
        }
        this.reverseGraph.get(dep)!.push(template.id);
      }
    }

    logger.debug('依赖图构建完成', {
      templates: templates.length,
      dependencies: this.getTotalDependencyCount()
    });
  }

  /**
   * 从模板定义中提取依赖关系
   * @param template 模板定义
   * @returns 依赖的模板ID列表
   */
  private extractDependencies(template: TemplateDefinition): string[] {
    const dependencies: string[] = [];

    // 1. 从 metadata.dependencies 中提取
    if (template.metadata?.dependencies) {
      dependencies.push(...template.metadata.dependencies);
    }

    // 2. 从步骤中的 useTemplate 动作提取
    if (template.steps) {
      this.extractDependenciesFromSteps(template.steps, dependencies);
    }

    // 去重并过滤无效依赖
    return [...new Set(dependencies)].filter(dep => 
      dep && dep !== template.id // 防止自依赖
    );
  }

  /**
   * 从步骤中递归提取依赖
   * @param steps 步骤列表
   * @param dependencies 依赖列表（会被修改）
   */
  private extractDependenciesFromSteps(steps: any[], dependencies: string[]): void {
    for (const step of steps) {
      if (step.action === 'useTemplate' && step.template) {
        dependencies.push(step.template);
      }

      // 处理嵌套步骤（如 conditionalStep, forEach 等）
      if (step.step && typeof step.step === 'object') {
        this.extractDependenciesFromSteps([step.step], dependencies);
      }

      if (step.steps && Array.isArray(step.steps)) {
        this.extractDependenciesFromSteps(step.steps, dependencies);
      }
    }
  }

  /**
   * 检测循环依赖
   * @param templates 模板列表
   * @returns 检测结果
   */
  checkCircularDependencies(templates: TemplateDefinition[]): CircularDependencyResult {
    this.buildDependencyGraph(templates);
    
    const cycles: string[][] = [];
    this.visiting.clear();
    this.visited.clear();

    // 对每个模板进行深度优先搜索
    for (const template of templates) {
      if (!this.visited.has(template.id)) {
        const cycle = this.dfsCheckCycle(template.id, []);
        if (cycle.length > 0) {
          cycles.push(cycle);
        }
      }
    }

    return {
      hasCircularDependency: cycles.length > 0,
      cycles,
      dependencyGraph: new Map(this.dependencyGraph)
    };
  }

  /**
   * 深度优先搜索检测循环
   * @param templateId 当前模板ID
   * @param path 当前路径
   * @returns 如果发现循环返回循环路径，否则返回空数组
   */
  private dfsCheckCycle(templateId: string, path: string[]): string[] {
    if (this.visiting.has(templateId)) {
      // 找到循环：从当前路径中找到循环起点
      const cycleStart = path.indexOf(templateId);
      if (cycleStart >= 0) {
        return path.slice(cycleStart).concat(templateId);
      }
      return [templateId]; // 自循环
    }

    if (this.visited.has(templateId)) {
      return []; // 已经检查过，无循环
    }

    this.visiting.add(templateId);
    const newPath = [...path, templateId];

    const dependencies = this.dependencyGraph.get(templateId) || [];
    for (const dep of dependencies) {
      // 检查依赖是否存在
      if (!this.dependencyGraph.has(dep)) {
        logger.warn(`模板 ${templateId} 依赖的模板 ${dep} 不存在`);
        continue;
      }

      const cycle = this.dfsCheckCycle(dep, newPath);
      if (cycle.length > 0) {
        return cycle;
      }
    }

    this.visiting.delete(templateId);
    this.visited.add(templateId);
    return [];
  }

  /**
   * 获取模板的拓扑排序
   * 用于确定模板的加载顺序
   * @returns 拓扑排序后的模板ID列表
   */
  getTopologicalOrder(): string[] {
    const inDegree = new Map<string, number>();
    const queue: string[] = [];
    const result: string[] = [];

    // 计算每个节点的入度
    for (const [templateId] of this.dependencyGraph) {
      inDegree.set(templateId, 0);
    }

    for (const [, dependencies] of this.dependencyGraph) {
      for (const dep of dependencies) {
        if (inDegree.has(dep)) {
          inDegree.set(dep, inDegree.get(dep)! + 1);
        }
      }
    }

    // 找到所有入度为0的节点
    for (const [templateId, degree] of inDegree) {
      if (degree === 0) {
        queue.push(templateId);
      }
    }

    // 拓扑排序
    while (queue.length > 0) {
      const current = queue.shift()!;
      result.push(current);

      const dependencies = this.dependencyGraph.get(current) || [];
      for (const dep of dependencies) {
        if (inDegree.has(dep)) {
          const newDegree = inDegree.get(dep)! - 1;
          inDegree.set(dep, newDegree);
          
          if (newDegree === 0) {
            queue.push(dep);
          }
        }
      }
    }

    return result;
  }

  /**
   * 获取模板的依赖信息
   * @param templateId 模板ID
   * @returns 依赖信息
   */
  getDependencyInfo(templateId: string): DependencyInfo | null {
    if (!this.dependencyGraph.has(templateId)) {
      return null;
    }

    return {
      templateId,
      dependencies: this.dependencyGraph.get(templateId) || [],
      dependents: this.reverseGraph.get(templateId) || []
    };
  }

  /**
   * 获取所有模板的依赖信息
   * @returns 依赖信息映射
   */
  getAllDependencyInfo(): Map<string, DependencyInfo> {
    const result = new Map<string, DependencyInfo>();

    for (const templateId of this.dependencyGraph.keys()) {
      const info = this.getDependencyInfo(templateId);
      if (info) {
        result.set(templateId, info);
      }
    }

    return result;
  }

  /**
   * 验证新模板是否会引入循环依赖
   * @param newTemplate 新模板
   * @param existingTemplates 现有模板
   * @returns 验证结果
   */
  validateNewTemplate(
    newTemplate: TemplateDefinition, 
    existingTemplates: TemplateDefinition[]
  ): {
    isValid: boolean;
    cycles: string[][];
    missingDependencies: string[];
  } {
    // 创建临时模板列表
    const tempTemplates = [...existingTemplates, newTemplate];
    
    // 检查依赖是否存在
    const newDependencies = this.extractDependencies(newTemplate);
    const existingIds = new Set(existingTemplates.map(t => t.id));
    const missingDependencies = newDependencies.filter(dep => !existingIds.has(dep));

    // 检查循环依赖
    const result = this.checkCircularDependencies(tempTemplates);

    return {
      isValid: !result.hasCircularDependency && missingDependencies.length === 0,
      cycles: result.cycles,
      missingDependencies
    };
  }

  /**
   * 获取依赖统计信息
   * @returns 统计信息
   */
  getDependencyStats(): {
    totalTemplates: number;
    totalDependencies: number;
    templatesWithDependencies: number;
    maxDependencyDepth: number;
  } {
    const totalTemplates = this.dependencyGraph.size;
    const totalDependencies = this.getTotalDependencyCount();
    const templatesWithDependencies = Array.from(this.dependencyGraph.values())
      .filter(deps => deps.length > 0).length;

    return {
      totalTemplates,
      totalDependencies,
      templatesWithDependencies,
      maxDependencyDepth: this.calculateMaxDepth()
    };
  }

  /**
   * 获取总依赖数量
   */
  private getTotalDependencyCount(): number {
    return Array.from(this.dependencyGraph.values())
      .reduce((total, deps) => total + deps.length, 0);
  }

  /**
   * 计算最大依赖深度
   */
  private calculateMaxDepth(): number {
    let maxDepth = 0;

    const calculateDepth = (templateId: string, visited: Set<string>): number => {
      if (visited.has(templateId)) {
        return 0; // 避免循环
      }

      visited.add(templateId);
      const dependencies = this.dependencyGraph.get(templateId) || [];
      
      if (dependencies.length === 0) {
        visited.delete(templateId);
        return 0;
      }

      let maxChildDepth = 0;
      for (const dep of dependencies) {
        const depth = calculateDepth(dep, visited);
        maxChildDepth = Math.max(maxChildDepth, depth);
      }

      visited.delete(templateId);
      return maxChildDepth + 1;
    };

    for (const templateId of this.dependencyGraph.keys()) {
      const depth = calculateDepth(templateId, new Set());
      maxDepth = Math.max(maxDepth, depth);
    }

    return maxDepth;
  }
}
