import { TestingEngine } from './testing-engine/testing-engine';

/**
 * 启动完整的测试系统
 * 包括浏览器连接、API注入、页面监听等完整功能
 */
async function startTestingSystem() {
    console.log('🚀 启动完整的自动化测试系统...');
      // 创建完整的测试引擎，包含所有必要组件
    const testingEngine = new TestingEngine({
      connectionType: 'cdp',
      headless: false,
      debuggingPort: 9222,
      cdpEndpoint: 'http://localhost:9222',
      timeout: 30000,
      outputDir: './test-results',
      captureScreenshots: false,
      continueOnFailure: false
    });

    // 系统会在第一次使用时自动初始化
    // 这里我们主动触发初始化以确保所有组件就绪
    console.log('📋 初始化测试系统组件...');
  await testingEngine.ensureInitialized();
  
}

// 启动系统
startTestingSystem().catch(console.error);