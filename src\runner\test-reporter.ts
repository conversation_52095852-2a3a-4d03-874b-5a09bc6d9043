import * as fs from 'node:fs';
import * as path from 'node:path';
import { logger } from '../utils/logger';
import type { TestRunnerOptions } from '../types';

/**
 * 测试运行结果接口
 */
export interface TestRunResult {
  success: boolean;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
  };
  results: any[];
  errors: string[];
}

/**
 * 测试报告生成器
 * 负责生成各种格式的测试报告
 */
export class TestReporter {
  private options: TestRunnerOptions;

  constructor(options: TestRunnerOptions = {}) {
    this.options = {
      outputDir: './test-results',
      browser: 'chromium',
      headless: true,
      timeout: 30000,
      ...options,
    };
  }

  /**
   * 生成测试报告
   */
  async generate(result: TestRunResult, format: 'json' | 'html' = 'json'): Promise<string> {
    // 确保输出目录存在
    this.ensureOutputDirectory();

    switch (format) {
      case 'json':
        return await this.generateJSONReport(result);
      case 'html':
        return await this.generateHTMLReport(result);
      default:
        throw new Error(`不支持的报告格式: ${format}`);
    }
  }

  /**
   * 生成JSON格式报告
   */
  private async generateJSONReport(result: TestRunResult): Promise<string> {
    const outputDir = this.options.outputDir!;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(outputDir, 'reports', `test-report-${timestamp}.json`);

    const reportData = {
      timestamp: new Date().toISOString(),
      summary: result.summary,
      results: result.results,
      errors: result.errors,
      environment: {
        browser: this.options.browser,
        headless: this.options.headless,
        timeout: this.options.timeout,
      },
    };

    // 确保reports目录存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    logger.info('JSON报告已生成', { reportPath });
    return reportPath;
  }

  /**
   * 生成HTML格式报告
   */
  private async generateHTMLReport(result: TestRunResult): Promise<string> {
    const outputDir = this.options.outputDir!;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(outputDir, 'reports', `test-report-${timestamp}.html`);

    const htmlContent = this.generateHTMLContent(result);

    // 确保reports目录存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, htmlContent);
    logger.info('HTML报告已生成', { reportPath });
    return reportPath;
  }

  /**
   * 生成HTML内容
   */
  private generateHTMLContent(result: TestRunResult): string {
    const { summary, results, errors } = result;
    const timestamp = new Date().toISOString();

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - ${timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .summary-item { padding: 15px; border-radius: 5px; text-align: center; }
        .passed { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
        .skipped { background: #fff3cd; color: #856404; }
        .total { background: #d1ecf1; color: #0c5460; }
        .results { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .test-passed { border-left-color: #28a745; }
        .test-failed { border-left-color: #dc3545; }
        .test-skipped { border-left-color: #ffc107; }
        .errors { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>自动化测试报告</h1>
        <p>生成时间: ${timestamp}</p>
        <p>浏览器: ${this.options.browser} (${this.options.headless ? '无头模式' : '有头模式'})</p>
    </div>

    <div class="summary">
        <div class="summary-item total">
            <h3>${summary.total}</h3>
            <p>总计</p>
        </div>
        <div class="summary-item passed">
            <h3>${summary.passed}</h3>
            <p>通过</p>
        </div>
        <div class="summary-item failed">
            <h3>${summary.failed}</h3>
            <p>失败</p>
        </div>
        <div class="summary-item skipped">
            <h3>${summary.skipped}</h3>
            <p>跳过</p>
        </div>
    </div>

    <div class="results">
        <h2>测试结果详情</h2>
        ${results.map(test => `
            <div class="test-result test-${test.status}">
                <h4>${test.name}</h4>
                <p>状态: ${test.status}</p>
                <p>耗时: ${test.duration}ms</p>
                ${test.error ? `<p>错误: ${test.error}</p>` : ''}
            </div>
        `).join('')}
    </div>

    ${errors.length > 0 ? `
        <div class="errors">
            <h2>错误信息</h2>
            <ul>
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        </div>
    ` : ''}
</body>
</html>`;
  }

  /**
   * 确保输出目录存在
   */
  private ensureOutputDirectory(): void {
    const outputDir = this.options.outputDir!;
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      logger.debug('创建输出目录', { outputDir });
    }
  }

  /**
   * 获取最新的报告文件路径
   */
  getLatestReport(format: 'json' | 'html' = 'json'): string | null {
    const outputDir = this.options.outputDir!;
    const reportsDir = path.join(outputDir, 'reports');
    
    if (!fs.existsSync(reportsDir)) {
      return null;
    }

    const extension = format === 'json' ? '.json' : '.html';
    const files = fs.readdirSync(reportsDir)
      .filter(file => file.startsWith('test-report-') && file.endsWith(extension))
      .sort()
      .reverse();

    return files.length > 0 ? path.join(reportsDir, files[0]) : null;
  }

  /**
   * 清理旧的报告文件
   */
  cleanupOldReports(keepCount: number = 10): void {
    const outputDir = this.options.outputDir!;
    const reportsDir = path.join(outputDir, 'reports');
    
    if (!fs.existsSync(reportsDir)) {
      return;
    }

    ['json', 'html'].forEach(format => {
      const extension = `.${format}`;
      const files = fs.readdirSync(reportsDir)
        .filter(file => file.startsWith('test-report-') && file.endsWith(extension))
        .sort()
        .reverse();

      if (files.length > keepCount) {
        const filesToDelete = files.slice(keepCount);
        filesToDelete.forEach(file => {
          const filePath = path.join(reportsDir, file);
          fs.unlinkSync(filePath);
          logger.debug('删除旧报告文件', { filePath });
        });
      }
    });
  }
}
