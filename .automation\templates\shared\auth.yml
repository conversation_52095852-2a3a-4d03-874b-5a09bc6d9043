templates:
  login-template:
    name: "登录页面模板"
    description: "蓝奥无人奶茶车调度系统登录页面的可复用模板"
    type: "shared"
    parameters:
      - name: username
        type: "string"
        required: true
        description: "登录账号"
      - name: password
        type: "string"
        required: true
        description: "登录密码"
      - name: rememberPassword
        type: "boolean"
        required: false
        default: false
        description: "是否记住账号密码"
      - name: expectedUrl
        type: "string"
        required: false
        default: "/"
        description: "登录成功后期望跳转的URL"
    steps:
      # 填写账号
      - action: fill
        role: textbox
        roleOptions:
          name: "*账号"
        data: "{{username}}"
        description: "输入登录账号"
      
      # 填写密码
      - action: fill
        role: textbox
        roleOptions:
          name: "*密码"
        data: "{{password}}"
        description: "输入登录密码"
      
      # 可选：勾选记住密码
      - action: check
        role: checkbox
        roleOptions:
          name: "记住账号密码"
        condition: "{{rememberPassword}}"
        description: "根据参数决定是否勾选记住密码"
      
      # 点击登录按钮
      - action: click
        role: button
        roleOptions:
          name: "登录"
        description: "点击登录按钮提交表单"
      
      # 等待页面跳转或加载
      - action: wait
        value: 2000
        description: "等待登录处理完成"

  logout-template:
    name: "退出登录模板"
    description: "清除用户登录状态并退出系统的可复用模板"
    type: "shared"
    parameters:
      - name: redirectToLogin
        type: "boolean"
        required: false
        default: true
        description: "是否重定向到登录页面"
      - name: baseUrl
        type: "string"
        required: false
        default: "http://localhost:9999"
        description: "应用的基础URL"
    steps:
      # 清除localStorage中的用户信息
      - action: executeScript
        script: "localStorage.removeItem('geeker-user')"
        description: "删除localStorage中的geeker-user字段"
      
      # 清除其他可能的用户相关存储
      - action: executeScript
        script: |
          // 清除可能的其他用户相关数据
          localStorage.removeItem('token');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('authMenuList');
          // 清除sessionStorage中的用户数据
          sessionStorage.clear();
        description: "清除其他用户相关的存储数据"
      
      # 可选：重定向到登录页面
      - action: navigate
        url: "{{baseUrl}}/#/login"
        condition: "{{redirectToLogin}}"
        description: "重定向到登录页面"
      
      # 等待页面加载
      - action: wait
        value: 1000
        description: "等待页面加载完成"

  login-verification-template:
    name: "登录验证模板"
    description: "验证登录结果的可复用模板"
    type: "shared"
    parameters:
      - name: expectedSuccessIndicator
        type: "string"
        required: false
        default: "欢迎"
        description: "登录成功的预期指示文本"
      - name: expectedErrorMessage
        type: "string"
        required: false
        default: "账号或密码错误"
        description: "登录失败的预期错误信息"
    steps:
      # 验证登录成功（可选）
      - action: verify
        type: text
        data: "{{expectedSuccessIndicator}}"
        assertion: visible
        description: "验证登录成功指示器是否可见"
      
      # 或验证登录失败消息（可选）
      - action: verify
        type: text
        data: "{{expectedErrorMessage}}"
        assertion: visible
        description: "验证登录失败消息是否显示"

  login-navigation-template:
    name: "登录页面导航模板"
    description: "导航到登录页面的可复用模板"
    type: "shared"
    parameters:
      - name: baseUrl
        type: "string"
        required: false
        default: "http://localhost:9999"
        description: "应用的基础URL"
    steps:
      # 导航到登录页面
      - action: navigate
        url: "{{baseUrl}}/#/login"
        description: "导航到登录页面"
      
      # 等待页面加载完成
      - action: waitForText
        data: "欢迎登录"
        description: "等待登录页面完全加载"

  complete-login-flow-template:
    name: "完整登录流程模板"
    description: "包含导航、登录和验证的完整流程模板"
    type: "shared"
    parameters:
      - name: baseUrl
        type: "string"
        required: false
        default: "http://localhost:9999"
      - name: username
        type: "string"
        required: true
      - name: password
        type: "string"
        required: true
      - name: rememberPassword
        type: "boolean"
        required: false
        default: false
      - name: verifySuccess
        type: "boolean"
        required: false
        default: true
        description: "是否验证登录成功"
    steps:
      # 使用导航模板
      - action: useTemplate
        template: "login-navigation-template"
        parameters:
          baseUrl: "{{baseUrl}}"
      
      # 使用登录模板
      - action: useTemplate
        template: "login-template"
        parameters:
          username: "{{username}}"
          password: "{{password}}"
          rememberPassword: "{{rememberPassword}}"
      
      # 可选：验证登录结果
      - action: useTemplate
        template: "login-verification-template"
        condition: "{{verifySuccess}}"
        parameters:
          expectedSuccessIndicator: "欢迎"

  complete-logout-flow-template:
    name: "完整退出登录流程模板"
    description: "包含清除存储和重定向的完整退出流程模板"
    type: "shared"
    parameters:
      - name: baseUrl
        type: "string"
        required: false
        default: "http://localhost:9999"
      - name: verifyLogout
        type: "boolean"
        required: false
        default: true
        description: "是否验证退出登录成功"
    steps:
      # 使用退出登录模板
      - action: useTemplate
        template: "logout-template"
        parameters:
          baseUrl: "{{baseUrl}}"
          redirectToLogin: true
      
      # 可选：验证退出登录成功
      - action: verify
        type: text
        data: "欢迎登录"
        assertion: visible
        condition: "{{verifyLogout}}"
        description: "验证已成功退出并返回登录页面"
