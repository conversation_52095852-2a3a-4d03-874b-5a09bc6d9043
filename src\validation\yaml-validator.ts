import * as yaml from 'yaml';
import Ajv from 'ajv';
import betterAjvErrors from 'better-ajv-errors';
import { YAMLSchemaExporter } from '../schema/yaml-schema-exporter';

export interface ValidationError {
  type: 'SYNTAX' | 'SCHEMA' | 'STRUCTURE' | 'LOGIC';
  severity: 'ERROR' | 'WARNING' | 'INFO';
  message: string;
  location?: {
    line: number;
    column: number;
    path?: string;
  };
  suggestions: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export class YAMLSyntaxValidator {
  private ajv: Ajv;
  private dslSchema: any;

  constructor() {
    // 启用 allErrors 以收集所有验证错误，而不是在第一个错误时停止
    this.ajv = new Ajv({ allErrors: true, verbose: true });

    try {
      this.dslSchema = YAMLSchemaExporter.exportJSONSchema();

      // 验证 schema 是否正确生成
      if (!this.dslSchema || typeof this.dslSchema !== 'object') {
        throw new Error('Schema generation failed: schema is null or not an object');
      }

      if (!this.dslSchema.properties && !this.dslSchema.anyOf) {
        throw new Error('Schema generation failed: schema missing properties or anyOf');
      }

    } catch (error) {
      console.error('Failed to initialize YAML schema:', error);
      // 提供一个基本的 fallback schema
      this.dslSchema = {
        type: 'object',
        properties: {},
        additionalProperties: true
      };
    }
  }

  /**
   * 根据错误信息和路径生成智能建议
   */
  private generateSuggestions(errorMessage: string, path: string): string[] {
    const suggestions: string[] = [];

    // 基于错误信息类型提供针对性建议
    if (errorMessage.includes('is not expected to be here')) {
      suggestions.push('检查属性名是否拼写正确');
      suggestions.push('确认该属性是否在当前上下文中被支持');
      suggestions.push('查看 schema 定义中允许的属性列表');
    } else if (errorMessage.includes('must be equal to one of the allowed values')) {
      suggestions.push('检查属性值是否在允许的枚举值范围内');
      suggestions.push('查看 schema 定义中该字段的可选值');
    } else if (errorMessage.includes('must have required property')) {
      suggestions.push('添加缺失的必需属性');
      suggestions.push('检查 schema 定义中的必需字段列表');
    } else if (errorMessage.includes('must be')) {
      suggestions.push('检查数据类型是否正确');
      suggestions.push('确认值的格式是否符合要求');
    } else {
      suggestions.push('查看 schema 定义，严格遵守 schema 定义的格式');
      suggestions.push('检查数据类型和必需字段');
    }

    // 基于路径提供上下文相关建议
    if (path.includes('/config')) {
      suggestions.push('参考配置文档中的有效配置选项');
    } else if (path.includes('/templates')) {
      suggestions.push('检查模板定义的格式和必需字段');
    } else if (path.includes('/tests')) {
      suggestions.push('确认测试步骤的 action 类型是否正确');
    } else if (path.includes('/hooks')) {
      suggestions.push('检查 hooks 的定义格式');
    }

    return suggestions.slice(0, 3); // 限制建议数量，避免信息过载
  }

  /**
   * 验证 YAML 内容（优化版：只解析一次）
   */
  validate(yamlContent: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 第一步：YAML 语法验证和解析（只解析一次）
      let parsed: any;
      try {
        parsed = yaml.parse(yamlContent);
      } catch (error: any) {
        const location = this.extractYAMLErrorLocation(error);
        result.errors.push({
          type: 'SYNTAX',
          severity: 'ERROR',
          message: `YAML 语法错误: ${error.message}`,
          location,
          suggestions: ["检查 YAML 文件格式是否正确，严格遵守 YAML 语法"]
        });
        result.isValid = false;
        return result;
      }

      // 第二步：使用标准 JSON Schema 验证
      const valid = this.ajv.validate(this.dslSchema, parsed);
      if (!valid && this.ajv.errors) {
        try {
          // 使用 better-ajv-errors 处理所有错误，获得更好的错误信息
          const betterErrors = betterAjvErrors(this.dslSchema, parsed, this.ajv.errors, {
            format: 'js'
          });

          // 使用 Set 来去重，避免重复的错误信息
          const uniqueErrors = new Set<string>();

          for (const betterError of betterErrors) {
            // 创建唯一标识符，基于路径和错误信息
            const errorKey = `${(betterError as any).path}:${(betterError as any).error}`;

            if (!uniqueErrors.has(errorKey)) {
              uniqueErrors.add(errorKey);

              result.errors.push({
                type: 'SCHEMA',
                severity: 'ERROR',
                message: (betterError as any).error,
                location: {
                  line: (betterError as any).start.line,
                  column: (betterError as any).start.column,
                  path: ((betterError as any).path as string).replace(/^\//, '').replace(/\//g, '.')
                },
                suggestions: this.generateSuggestions((betterError as any).error, (betterError as any).path)
              });
            }
          }
        } catch (betterAjvError) {

          // 基本的错误处理逻辑
          const uniquePaths = new Set<string>();
          for (const error of this.ajv.errors) {
            const path = error.instancePath || 'root';
            if (!uniquePaths.has(path)) {
              uniquePaths.add(path);

              result.errors.push({
                type: 'SCHEMA',
                severity: 'ERROR',
                message: `Schema validation failed at ${path}: ${error.message}`,
                location: path !== 'root' ? {
                  line: 0,
                  column: 0,
                  path: path.replace(/^\//, '').replace(/\//g, '.')
                } : undefined,
                suggestions: [
                  "查看 schema 定义，严格遵守 schema 定义的格式",
                  "检查数据类型和必需字段"
                ]
              });
            }
          }
        }
      }

      // 第三步：最佳实践检查（警告）
      const warnings = this.checkBestPractices(parsed);
      result.warnings.push(...warnings);

      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.isValid = false;
      result.errors.push({
        type: 'SYNTAX',
        severity: 'ERROR',
        message: `Validation failed: ${error instanceof Error ? error.message : String(error) }`,
        suggestions: ['检查 YAML 文件格式是否正确']
      });
    }

    return result;
  }


  /**
   * 检查最佳实践
   */
  private checkBestPractices(_parsed: any): ValidationError[] {
    const warnings: ValidationError[] = [];

    return warnings;
  }

  /**
   * 提取 YAML 错误位置
   */
  private extractYAMLErrorLocation(error: any): { line: number; column: number } | undefined {
    if (error.mark) {
      return {
        line: error.mark.line + 1,
        column: error.mark.column + 1
      };
    }
    return undefined;
  }

}
