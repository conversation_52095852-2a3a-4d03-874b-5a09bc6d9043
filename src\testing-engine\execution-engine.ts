import { logger } from '../utils/logger';
import { DSLParser } from '../dsl/parser';
import type { TestResult, ResolvedConfig } from './types';
import type { IExecutorService } from '../infrastructure/executor-service';
import { DataAccessor } from './data-accessor';
import type { TemplateResolver } from '../template/template-resolver';
import type { ScriptManager } from '../script/script-manager';
import type { IResourceService } from '../infrastructure/resource-service';
import type { TestStep, ExecutorType } from '../types';
import type { ParsedTestSuite } from '../dsl/parser';
import type { Browser } from '@playwright/test';

/**
 * 执行上下文接口
 */
export interface ExecutionContext {
  config: ResolvedConfig;
  dataAccessor: DataAccessor;
  resourceService?: IResourceService;
  templateResolver?: TemplateResolver;  // 替代 templateFactory
  scriptManager?: ScriptManager;
  page?: any;
  pageId?: string;
  browser?: Browser;
}

/**
 * 执行引擎
 * 专门负责测试执行逻辑，符合单一职责原则
 */
export class ExecutionEngine {
  private executorService: IExecutorService;

  constructor(executorService: IExecutorService) {
    this.executorService = executorService;
    logger.debug('ExecutionEngine 初始化完成');
  }

  /**
   * 执行测试套件
   * 统一的测试套件执行逻辑，包含hooks处理
   */
  async executeTestSuite(testSuite: ParsedTestSuite, context: ExecutionContext): Promise<TestResult> {
    const startTime = Date.now();
    const result: TestResult = {
      success: true,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0,
      },
      results: [],
      errors: [],
    };

    logger.info('ExecutionEngine 开始执行测试套件', {
      testCount: testSuite.tests?.length || 0,
      hasHooks: !!(testSuite.hooks)
    });

    let beforeAllExecuted = false;
    let testsStarted = false;
    let afterAllExecuted = false;

    try {
      // 执行 beforeAll hooks
      if (testSuite.hooks?.beforeAll) {
        logger.info('执行 beforeAll hooks');
        await this.executeHooks('beforeAll', testSuite.hooks.beforeAll, context);
        beforeAllExecuted = true;
      }

      // 执行测试用例
      const tests = testSuite.tests || [];
      result.summary.total = tests.length;
      testsStarted = true;

      for (const [index, test] of tests.entries()) {
        logger.info(`执行测试 ${index + 1}/${tests.length}: ${test.name || test.id}`);

        try {
          // 执行 beforeEach hooks
          if (testSuite.hooks?.beforeEach) {
            logger.debug('执行 beforeEach hooks');
            await this.executeHooks('beforeEach', testSuite.hooks.beforeEach, context);
          }

          // 执行测试步骤
          const testResult = await this.executeTest(test, context);
          result.results.push(testResult);

          if (testResult.success) {
            result.summary.passed++;
          } else {
            result.summary.failed++;
            if (testResult.error) {
              result.errors.push(`测试 ${test.name || test.id} 失败: ${testResult.error}`);
            }
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logger.error(`测试执行失败: ${test.name || test.id}`, { error: errorMessage });

          result.summary.failed++;
          result.errors.push(`测试 ${test.name || test.id} 异常: ${errorMessage}`);

          // 根据配置决定是否继续执行
          if (!context.config.continueOnFailure) {
            logger.warn('测试失败，停止执行后续测试，但仍会执行清理 hooks');
            break;
          }
        } finally {
          // 确保 afterEach hooks 总是执行，即使测试失败
          if (testSuite.hooks?.afterEach) {
            try {
              logger.debug('执行 afterEach hooks');
              await this.executeHooks('afterEach', testSuite.hooks.afterEach, context);
            } catch (hookError) {
              const hookErrorMessage = hookError instanceof Error ? hookError.message : String(hookError);
              logger.error('afterEach hook 执行失败', { error: hookErrorMessage });
              result.errors.push(`afterEach hook 失败: ${hookErrorMessage}`);
            }
          }
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('ExecutionEngine 测试套件执行失败', { error: errorMessage });

      result.success = false;
      result.errors.push(`执行失败: ${errorMessage}`);

    } finally {
      // 确保 afterAll hooks 总是执行（只要 beforeAll 执行过或测试开始过）
      const shouldExecuteAfterAll = (beforeAllExecuted || testsStarted) && !!testSuite.hooks?.afterAll;

      logger.debug('afterAll 执行条件检查', {
        beforeAllExecuted,
        testsStarted,
        hasAfterAllHooks: !!testSuite.hooks?.afterAll,
        shouldExecuteAfterAll
      });

      if (shouldExecuteAfterAll) {
        try {
          logger.info('执行 afterAll hooks（清理资源）');
          await this.executeHooks('afterAll', testSuite.hooks!.afterAll!, context);
          afterAllExecuted = true;
          logger.info('afterAll hooks 执行成功');
        } catch (hookError) {
          const hookErrorMessage = hookError instanceof Error ? hookError.message : String(hookError);
          logger.error('afterAll hook 执行失败', { error: hookErrorMessage });
          result.errors.push(`afterAll hook 失败: ${hookErrorMessage}`);
          result.success = false;
          afterAllExecuted = false; // 执行失败
        }
      } else {
        logger.info('跳过 afterAll hooks 执行', {
          reason: !testSuite.hooks?.afterAll ? 'no afterAll hooks defined' : 'conditions not met'
        });
        afterAllExecuted = false;
      }

      // 计算最终结果
      if (result.success === undefined || result.success === true) {
        result.success = result.summary.failed === 0 && result.errors.length === 0;
      }
      result.summary.duration = Date.now() - startTime;

      logger.info('ExecutionEngine 测试套件执行完成', {
        success: result.success,
        summary: result.summary,
        errorsCount: result.errors.length,
        afterAllExecuted,
        shouldHaveExecutedAfterAll: shouldExecuteAfterAll
      });
    }

    return result;
  }

  /**
   * 执行单个测试
   */
  async executeTest(test: any, context: ExecutionContext): Promise<any> {
    const startTime = Date.now();

    try {
      // 创建执行上下文
      const executionContext = this.createExecutorContext(context);

      // 执行测试步骤
      await this.executorService.executeTest(
        test.id || `test-${Date.now()}`,
        test.steps || [],
        executionContext,
        {
          timeout: context.config.timeout,
          retries: 1, // 测试步骤可以重试
          continueOnFailure: context.config.continueOnFailure,
          captureScreenshots: context.config.captureScreenshots,
        }
      );

      const duration = Date.now() - startTime;

      // 如果 executorService.executeTest 没有抛出异常，说明测试成功
      return {
        success: true,
        duration,
        name: test.name || test.id,
        steps: test.steps?.length || 0,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        success: false,
        duration,
        name: test.name || test.id,
        error: errorMessage,
      };
    }
  }

  /**
   * 执行 hooks
   */
  async executeHooks(
    hookType: 'beforeAll' | 'afterAll' | 'beforeEach' | 'afterEach',
    hooksSteps: any[],
    context: ExecutionContext
  ): Promise<void> {
    if (!hooksSteps || hooksSteps.length === 0) {
      return;
    }

    logger.debug(`ExecutionEngine 执行 ${hookType} hooks`, { stepCount: hooksSteps.length });

    try {
      // 标准化 hooks 步骤，确保有 id 和 name
      const normalizedSteps = hooksSteps.map((step, index) => {
        return DSLParser.normalizeStep(step, index);
      });

      // 创建执行上下文
      const executionContext = this.createExecutorContext(context);

      // 执行 hooks 步骤
      await this.executorService.executeTest(
        `${hookType}-hooks-${Date.now()}`,
        normalizedSteps,
        executionContext,
        {
          timeout: context.config.timeout,
          retries: hookType === 'afterAll' ? 2 : 0, // afterAll 重试更多次
          continueOnFailure: hookType === 'afterAll', // afterAll 允许部分失败
          captureScreenshots: context.config.captureScreenshots,
        }
      );

      logger.debug(`ExecutionEngine ${hookType} hooks 执行完成`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (hookType === 'afterAll') {
        // afterAll 失败只记录错误，不阻断流程
        logger.error(`ExecutionEngine ${hookType} hooks 执行失败，但继续完成测试`, { error: errorMessage });
        // 不抛出错误，让测试继续完成
      } else {
        logger.error(`ExecutionEngine ${hookType} hooks 执行失败`, { error: errorMessage });
        throw new Error(`${hookType} hooks 执行失败: ${errorMessage}`);
      }
    }
  }

  /**
   * 创建 ExecutorService 需要的执行上下文
   */
  private createExecutorContext(context: ExecutionContext): ExecutionContext {
    return {
      config: context.config,
      dataAccessor: context.dataAccessor,
      page: context.page || (context.resourceService ? context.resourceService.getPage() : undefined),
      browser: context.resourceService?.getBrowser(),
      resourceService: context.resourceService,
      templateResolver: context.templateResolver,
      scriptManager: context.scriptManager,
    };
  }

  /**
   * 执行YAML内容
   * 统一的YAML执行入口，支持所有调用场景  todo 修改单独执行的脚本 也必须使用 dataAccessor 完整路径才可以
   */
/*   async executeYamlContent(yamlContent: string, options: any = {}): Promise<TestResult> {
    logger.info('ExecutionEngine 开始执行YAML内容');

    try {
      // 解析YAML
      const parseResult = DSLParser.parseWithValidation(yamlContent);
      if (!parseResult.isValid) {
        throw new Error(`YAML解析失败: ${parseResult.errors.map(e => e.message).join(', ')}`);
      }

      const testSuite = (parseResult as any).testSuite!;

      // 创建基本执行上下文（简化版）
      const context: ExecutionContext = {
        config: {
          timeout: options.timeout || 30000,
          baseUrl: options.baseUrl || '',
          retries: options.retries || 0,
          captureScreenshots: options.captureScreenshots || true,
          headless: options.headless || true,
          slowMo: options.slowMo || 0,
          outputDir: options.outputDir || './test-results',
          continueOnFailure: options.continueOnFailure || false,
          executorType: options.executorType || 'web',
          connectionType: 'new-browser',
          autoPort: false,
          templateDirs: [],
        } as ResolvedConfig,
        dataAccessor: {} as DataAccessor, // 简化的数据访问器
        page: options.page,
        pageId: options.pageId,
      };

      // 执行测试套件
      return await this.executeTestSuite(testSuite, context);

    } catch (error) {
      logger.error('YAML内容执行失败', {
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          skipped: 0,
          duration: 0,
        },
        results: [],
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  } */

  /**
   * 执行单个步骤
   * 支持页面调用和后台调用
   */
/*   async executeStep(step: TestStep, options: {executorType?: ExecutorType; page?: any; pageContext?: any} = {}): Promise<any> {
    logger.warn('ExecutionEngine.executeStep 已废弃，建议使用 TestingEngine.executeStep 获得完整功能支持');
    logger.info('ExecutionEngine 开始执行单个步骤', { action: step.action });

    try {
      // 创建简化的执行上下文（缺少 script/template 系统）
      const context: ExecutionContext = {
        config: {
          timeout: 30000,
          captureScreenshots: true,
          executorType: options.executorType || 'web',
          headless: false,
          slowMo: 0,
          connectionType: 'new-browser',
          autoPort: false,
          templateDirs: [],
          baseUrl: '',
          retries: 0,
          outputDir: './test-results',
          continueOnFailure: false,
        } as any,
        dataAccessor: new DataAccessor({}),
        page: options.pageContext?.page || options.page,
        pageId: options.pageContext?.pageId,
      };

      // 执行步骤
      await this.executorService.executeStep(step, context, options.executorType);

      return {
        success: true,
        duration: 0,
        action: step.action,
      };

    } catch (error) {
      logger.error('步骤执行失败', {
        action: step.action,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        duration: 0,
        action: step.action,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
 */
  /**
   * 执行模板
   * 支持模板参数化执行，使用TemplateResolver进行模板解析
   */
  async executeTemplate(
    templateId: string,
    parameters: Record<string, any> = {},
    context: ExecutionContext
  ): Promise<any> {
    const startTime = Date.now();
    logger.info('ExecutionEngine 开始执行模板', { templateId, parameters });

    try {
      // 检查TemplateResolver是否可用
      if (!context.templateResolver) {
        throw new Error('TemplateResolver 未初始化');
      }

      // 解析模板
      const resolution = context.templateResolver.resolveTemplate(templateId);
      if (!resolution.template) {
        throw new Error(`模板未找到: ${templateId}`);
      }

      logger.info('模板解析成功', {
        templateId,
        source: resolution.source,
        stepCount: resolution.template.steps?.length || 0
      });

      // 验证模板参数
      const paramValidation = context.templateResolver.validateTemplateParameters(templateId, parameters);
      if (!paramValidation.isValid) {
        throw new Error(`模板参数验证失败: ${paramValidation.errors.join('; ')}`);
      }

      // 执行模板步骤
      const stepResults = [];
      const template = resolution.template;

      for (let i = 0; i < (template.steps?.length || 0); i++) {
        const step = template.steps![i];

        logger.debug('执行模板步骤', {
          templateId,
          stepIndex: i,
          action: step.action
        });

        // 替换步骤中的参数占位符
        const processedStep = this.processStepParameters(step, parameters);

        // 执行步骤
        const stepResult = await this.executorService.executeStep(processedStep, context);
        stepResults.push(stepResult);
      }

      const duration = Date.now() - startTime;

      logger.info('模板执行完成', {
        templateId,
        duration: `${duration}ms`,
        stepCount: stepResults.length,
        source: resolution.source
      });

      return {
        success: true,
        duration,
        templateId,
        parameters,
        source: resolution.source,
        stepResults,
        stepCount: stepResults.length
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error('模板执行失败', {
        templateId,
        duration: `${duration}ms`,
        error: errorMessage
      });

      return {
        success: false,
        duration,
        templateId,
        parameters,
        error: errorMessage
      };
    }
  }

  /**
   * 处理步骤参数，替换模板占位符
   */
  private processStepParameters(step: any, parameters: Record<string, any>): any {
    // 深拷贝步骤，避免修改原始数据
    const processedStep = JSON.parse(JSON.stringify(step));

    // 递归替换所有字符串值中的占位符
    this.replaceParametersInObject(processedStep, parameters);

    return processedStep;
  }

  /**
   * 递归替换对象中的参数占位符
   */
  private replaceParametersInObject(obj: any, parameters: Record<string, any>): void {
    if (typeof obj === 'string') {
      // 字符串类型在上层处理
      return;
    }

    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        if (typeof obj[i] === 'string') {
          obj[i] = this.replaceParametersInString(obj[i], parameters);
        } else if (typeof obj[i] === 'object' && obj[i] !== null) {
          this.replaceParametersInObject(obj[i], parameters);
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          obj[key] = this.replaceParametersInString(obj[key], parameters);
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          this.replaceParametersInObject(obj[key], parameters);
        }
      }
    }
  }

  /**
   * 替换字符串中的参数占位符
   */
  private replaceParametersInString(str: string, parameters: Record<string, any>): string {
    return str.replace(/\{\{(\w+)\}\}/g, (match, paramName) => {
      if (paramName in parameters) {
        return String(parameters[paramName]);
      }
      logger.warn('模板参数未找到', { paramName, availableParams: Object.keys(parameters) });
      return match; // 保持原样
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    logger.debug('ExecutionEngine 资源清理完成');
  }
}
