/**
 * 脚本系统类型定义
 * 遵循YAGNI原则，只定义必需的类型
 */

/**
 * 脚本参数定义
 */
export interface ScriptParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  default?: any;
  description?: string;
}

/**
 * 脚本返回值定义
 */
export interface ScriptReturn {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
}

/**
 * 脚本元数据
 */
export interface ScriptMetadata {
  name: string;
  description: string;
  version?: string;
  author?: string;
  parameters?: Record<string, ScriptParameter>;
  returns?: Record<string, ScriptReturn>;
  tags?: string[];
}

/**
 * 脚本定义
 */
export interface ScriptDefinition {
  name: string;
  filePath: string;
  execute: (params: any, context: ScriptExecutionContext) => Promise<any>;
  metadata: ScriptMetadata;
}

/**
 * 脚本执行上下文
 */
export interface ScriptExecutionContext {
  page: any; // Playwright Page
  logger: any; // Logger instance
  dataAccessor: any; // DataAccessor instance
  variables: Record<string, any>; // 测试变量
}

/**
 * 脚本执行结果
 */
export interface ScriptExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
}

/**
 * useScript Action 定义
 */
export interface UseScriptAction {
  action: 'useScript';
  script: string;
  parameters?: Record<string, any>;
  returnAs?: string;
  timeout?: number;
  description?: string;
}

/**
 * 脚本加载结果
 */
export interface ScriptLoadResult {
  success: boolean;
  scripts: ScriptDefinition[];
  errors: string[];
}
