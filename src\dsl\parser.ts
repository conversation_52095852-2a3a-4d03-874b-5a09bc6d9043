import * as yaml from 'yaml';
import type { TestSuiteConfig, TestStep, Template, ExecutorType } from '../types';
import { logger } from '../utils/logger';
import { validateRequired } from '../utils/helpers';
import * as fs from 'node:fs';
import { YAMLSyntaxValidator, ValidationResult, ValidationError } from '../validation/yaml-validator';



export interface ParseWithValidationResult extends ValidationResult {
  parsed?: ParsedTestSuite;  // 只有验证通过才有这个字段
}

export interface ParsedTestSuite {
  config: TestSuiteConfig;
  hooks?: ParsedHooks;
  tests: ParsedTest[];
  templates?: Template[];
  inlineTemplates?: Map<string, Template>;  // 新增：内联模板Map
}

export interface ParsedHooks {
  beforeEach?: TestStep[];
  afterEach?: TestStep[];
  beforeAll?: TestStep[];
  afterAll?: TestStep[];
}

export interface ParsedTest {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  steps: TestStep[];
  data?: Record<string, any>;
  config?: TestLevelConfig;  // 测试级别配置
}

// 测试级别配置接口
export interface TestLevelConfig {
  executor?: ExecutorType;  // 测试级别的执行器配置
  timeout?: number;
  retries?: number;
  // 可以根据需要添加其他测试级别的配置
}

export class DSLParser {
  private static validator: YAMLSyntaxValidator;

  /**
   * 初始化验证器（懒加载）
   */
  private static initializeValidator() {
    if (!this.validator) {
      this.validator = new YAMLSyntaxValidator();
    }
  }

  /**
   * 统一的YAML验证和解析方法
   * 复用 YAMLSyntaxValidator，然后进行结构解析
   */
  static parseWithValidation(content: string): ParseWithValidationResult {
    // 第一步：使用 YAMLSyntaxValidator 进行基础验证
    this.initializeValidator();
    const validationResult = this.validator.validate(content);

    const result: ParseWithValidationResult = {
      isValid: validationResult.isValid,
      errors: [...validationResult.errors],
      warnings: [...validationResult.warnings]
    };

    // 第二步：如果基础验证通过，进行结构验证和转换 
    if (validationResult.isValid) {
      try {
        const parsed = yaml.parse(content);
        result.parsed = this.validateAndTransform(parsed);
      } catch (error) {
        result.errors.push({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `结构验证失败: ${error instanceof Error ? error.message : String(error)}`,
          suggestions: ['检查YAML结构是否符合DSL规范']
        });
        result.isValid = false;
      }
    }

    return result;
  }

  /**
   * Parse YAML test configuration (保持向后兼容)
   */
  static parse(content: string): ParsedTestSuite {
    const result = this.parseWithValidation(content);
    if (!result.isValid) {
      const errorMessages = result.errors.map(error => error.message).join('; ');
      logger.error('Failed to parse YAML content', { errors: result.errors });
      throw new Error(`YAML validation failed: ${errorMessages}`);
    }
    return result.parsed!;
  }

  /**
   * Parse test file
   */
  static parseFile(filePath: string): ParsedTestSuite {
    const content = fs.readFileSync(filePath, 'utf-8');
    logger.info('Parsing test file', { filePath });
    return this.parse(content);
  }

  /**
   * Validate and transform parsed content
   */
  private static validateAndTransform(parsed: any): ParsedTestSuite {
    validateRequired(parsed, ['config'], 'Test suite');

    // 解析内联模板
    const inlineTemplates = parsed.templates ? this.parseTemplates(parsed.templates) : [];
    const inlineTemplateMap = new Map(inlineTemplates.map(t => [t.name, t]));

    const result: ParsedTestSuite = {
      config: this.parseConfig(parsed.config),
      hooks: parsed.hooks ? this.parseHooks(parsed.hooks) : undefined,
      tests: [],
      templates: inlineTemplates,
      inlineTemplates: inlineTemplateMap,  // 新增：内联模板Map
    };

    // Parse individual tests
    if (parsed.tests) {
      if (Array.isArray(parsed.tests)) {
        result.tests = parsed.tests.map((test: any) => this.parseTest(test));
      } else {
        // Tests as object with keys
        result.tests = Object.entries(parsed.tests).map(([key, test]: [string, any]) => {
          return this.parseTest({ ...test, id: key });
        });
      }
    }

    // Parse test scenarios (alternative format)
    if (parsed.scenarios) {
      const scenarios = Array.isArray(parsed.scenarios) 
        ? parsed.scenarios 
        : Object.entries(parsed.scenarios).map(([key, scenario]: [string, any]) => ({ ...scenario, id: key }));

      result.tests.push(...scenarios.map((scenario: any) => this.parseTest(scenario)));
    }

    return result;
  }

  /**
   * Parse test suite configuration
   */
  private static parseConfig(config: any): TestSuiteConfig {
    validateRequired(config, ['name'], 'Test config');

    return {
      name: config.name,
      description: config.description,
      baseUrl: config.baseUrl,
      timeout: config.timeout || 30000,
      retries: config.retries || 1,
      parallel: config.parallel !== false,
      testMode: config.testMode || 'flow',
      captureScreenshots: config.captureScreenshots,
      continueOnFailure: config.continueOnFailure,
      variables: config.variables || {},
      dataSources: config.dataSources,
      hooks: config.hooks,
      executorType: config.executorType,
    };
  }



  /**
   * Parse hooks configuration (从 hooks 对象中解析)
   */
  private static parseHooks(hooks: any): ParsedHooks {
    const result: ParsedHooks = {};

    if (hooks.beforeEach) {
      result.beforeEach = this.parseSteps(hooks.beforeEach);
    }

    if (hooks.afterEach) {
      result.afterEach = this.parseSteps(hooks.afterEach);
    }

    if (hooks.beforeAll) {
      result.beforeAll = this.parseSteps(hooks.beforeAll);
    }

    if (hooks.afterAll) {
      result.afterAll = this.parseSteps(hooks.afterAll);
    }

    return result;
  }

  /**
   * Parse individual test
   */
  private static parseTest(test: any): ParsedTest {
    validateRequired(test, ['name', 'steps'], 'Test');

    return {
      id: test.id || test.name.toLowerCase().replace(/\s+/g, '-'),
      name: test.name,
      description: test.description,
      tags: test.tags || [],
      steps: this.parseSteps(test.steps),
      data: test.data,
    };
  }

  /**
   * Parse test steps
   */
  private static parseSteps(steps: any[]): TestStep[] {
    if (!Array.isArray(steps)) {
      throw new Error('Steps must be an array');
    }

    return steps.map((step, index) => this.parseStep(step, index));
  }

  /**
   * Parse individual test step
   */
  private static parseStep(step: any, index: number): TestStep {
    // Handle string shorthand
    if (typeof step === 'string') {
      return this.parseStringStep(step, index);
    }

    // Handle object step
    if (typeof step === 'object') {
      return this.parseObjectStep(step, index);
    }

    throw new Error(`Invalid step format at index ${index}`);
  }

  /**
   * Parse string step (deprecated - use object format)
   */
  private static parseStringStep(step: string, _index: number): TestStep {
    // For strict DSL, we don't support string format steps
    throw new Error(
      `String format steps are not supported in strict DSL mode. Please use object format for step: "${step}"\n
      Example:\n
      - action: "click"\n
        selector: "[data-testid='button']"\n\n
      For template calls:\n
      - action: "useTemplate"\n
        template: "template-name"\n
        parameters:\n
          key: "value"`,
    );
  }

  /**
   * Normalize step by adding required fields (id, name, type)
   * This method can be used for both regular parsing and template step processing
   */
  static normalizeStep(step: any, index: number): TestStep {
    // Validate required action field
    if (!step.action) {
      throw new Error(`Step at index ${index} must have an 'action' field`);
    }

    const id = step.id || `step-${index + 1}`;
    const name = step.name || `${step.action} step`;

    // Build the test step with strict validation
    const testStep: TestStep = {
      id,
      name,
      type: step.type || 'action',
      action: step.action,
      ...step
    };

    return testStep;
  }

  /**
   * Parse object step (strict DSL format)
   */
  private static parseObjectStep(step: any, index: number): TestStep {
    // Use the normalized step logic
    return this.normalizeStep(step, index);
  }


  /**
   * Parse templates
   */
  private static parseTemplates(templates: any): Template[] {
    if (Array.isArray(templates)) {
      return templates.map(template => this.parseTemplate(template));
    }

    // Templates as object
    return Object.entries(templates).map(([key, template]: [string, any]) => {
      return this.parseTemplate({ ...template, name: key });
    });
  }

  /**
   * Parse individual template
   */
  private static parseTemplate(template: any): Template {
    validateRequired(template, ['name', 'steps'], 'Template');

    return {
      name: template.name,
      type: template.type || 'business',
      description: template.description,
      parameters: template.parameters || [],
      steps: this.parseSteps(template.steps),
      dependencies: template.dependencies,
    };
  }

  // === 验证方法（从ValidationService迁移） ===

  /**
   * 验证测试步骤
   * 统一的步骤验证逻辑
   */
  static validateStep(step: TestStep): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // 基础字段验证
    if (!step.action) {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: '步骤缺少必需的action字段',
        suggestions: ['为步骤添加action字段，如: action: "click"'],
      });
    }

    // 动作特定验证
    this.validateActionSpecificFields(step, errors, warnings);

    // 选择器验证
    this.validateSelectors(step, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证配置
   * 统一的配置验证逻辑
   */
  static validateConfig(config: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // 执行器类型验证
    if (config.executorType && !this.isValidExecutorType(config.executorType)) {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: `不支持的执行器类型: ${config.executorType}`,
        suggestions: ['使用支持的执行器类型: web, element-plus'],
      });
    }

    // 超时配置验证
    if (config.timeout && (typeof config.timeout !== 'number' || config.timeout <= 0)) {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: '超时配置必须是正数',
        suggestions: ['设置合理的超时时间，如: timeout: 30000'],
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 批量验证步骤
   */
  static validateSteps(steps: TestStep[]): ValidationResult {
    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationError[] = [];

    steps.forEach((step, index) => {
      const result = this.validateStep(step);

      // 为错误添加步骤索引信息
      result.errors.forEach(error => {
        allErrors.push({
          ...error,
          location: error.location ? { ...error.location, path: `steps[${index}]` } : undefined,
        });
      });

      result.warnings.forEach(warning => {
        allWarnings.push({
          ...warning,
          location: warning.location ? { ...warning.location, path: `steps[${index}]` } : undefined,
        });
      });
    });

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
    };
  }

  // === 私有验证方法 ===

  /**
   * 验证动作特定字段
   */
  private static validateActionSpecificFields(step: TestStep, errors: ValidationError[], warnings: ValidationError[]): void {
    switch (step.action) {
      case 'click':
      case 'fill':
      case 'check':
        if (!step.selector && !step.role) {
          errors.push({
            type: 'STRUCTURE',
            severity: 'ERROR',
            message: `${step.action}动作需要selector或role字段`,
            suggestions: ['添加selector或role字段来定位元素'],
          });
        }
        break;

      case 'navigate':
        if (!step.url && !step.data) {
          errors.push({
            type: 'STRUCTURE',
            severity: 'ERROR',
            message: 'navigate动作需要url或data字段',
            suggestions: ['添加url字段指定导航地址'],
          });
        }
        break;

      case 'useTemplate':
        if (!step.template) {
          errors.push({
            type: 'STRUCTURE',
            severity: 'ERROR',
            message: 'useTemplate动作需要template字段',
            suggestions: ['添加template字段指定模板ID'],
          });
        }
        break;

      case 'wait':
        if (!step.duration && !step.selector && !step.condition) {
          warnings.push({
            type: 'LOGIC',
            severity: 'WARNING',
            message: 'wait动作建议指定duration、selector或condition',
            suggestions: ['添加等待条件以提高测试可靠性'],
          });
        }
        break;
    }
  }

  /**
   * 验证选择器
   */
  private static validateSelectors(step: TestStep, errors: ValidationError[], warnings: ValidationError[]): void {
    // 检查是否同时使用了多种选择器
    const selectorCount = [step.selector, step.role, (step as any).text].filter(Boolean).length;

    if (selectorCount > 1) {
      warnings.push({
        type: 'LOGIC',
        severity: 'WARNING',
        message: '同时使用多种选择器可能导致冲突',
        suggestions: ['建议只使用一种选择器方式'],
      });
    }

    // 验证role选择器的参数
    if (step.role && step.roleOptions) {
      if (typeof step.roleOptions !== 'object') {
        errors.push({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: 'roleOptions必须是对象',
          suggestions: ['设置正确的roleOptions对象'],
        });
      }
    }

    // 验证 within 上下文配置
    if (step.within) {
      this.validateWithinContext(step.within, errors, warnings);
    }
  }

  /**
   * 验证 within 上下文配置
   */
  private static validateWithinContext(within: any, errors: ValidationError[], warnings: ValidationError[]): void {
    // 检查 within 是否为对象
    if (typeof within !== 'object' || within === null) {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: 'within 必须是对象',
        suggestions: ['设置正确的 within 对象，包含 role 或 selector 字段'],
      });
      return;
    }

    // 检查是否提供了有效的定位方式
    const hasRole = Boolean(within.role);
    const hasSelector = Boolean(within.selector);

    if (!hasRole && !hasSelector) {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: 'within 必须包含 role 或 selector 字段',
        suggestions: ['在 within 中添加 role 或 selector 字段来定位容器元素'],
      });
    }

    // 检查是否同时使用了多种定位方式
    if (hasRole && hasSelector) {
      warnings.push({
        type: 'LOGIC',
        severity: 'WARNING',
        message: 'within 中同时使用 role 和 selector 可能导致冲突',
        suggestions: ['建议在 within 中只使用一种定位方式'],
      });
    }

    // 验证 roleOptions（如果存在）
    if (within.role && within.roleOptions) {
      if (typeof within.roleOptions !== 'object') {
        errors.push({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: 'within.roleOptions 必须是对象',
          suggestions: ['设置正确的 roleOptions 对象'],
        });
      }
    }

    // 验证 selector 类型（如果存在）
    if (within.selector && typeof within.selector !== 'string') {
      errors.push({
        type: 'STRUCTURE',
        severity: 'ERROR',
        message: 'within.selector 必须是字符串',
        suggestions: ['设置正确的 CSS 选择器字符串'],
      });
    }
  }

  /**
   * 检查是否为有效的执行器类型
   */
  private static isValidExecutorType(executorType: string): executorType is ExecutorType {
    return ['web', 'element-plus'].includes(executorType);
  }
}