/**
 * 模板解析器
 * 实现内联模板优先级机制，支持并发安全的模板查找
 */

import { logger } from '../utils/logger';
import type { TemplateRegistry, TemplateDefinition } from './template-registry';
import type { Template } from '../types';

/**
 * 模板解析结果
 */
export interface TemplateResolutionResult {
  template: TemplateDefinition | null;
  source: 'inline' | 'global' | 'none';
  templateId: string;
}

/**
 * 模板解析器
 * 负责按优先级查找模板：内联模板 > 全局注册模板
 */
export class TemplateResolver {
  constructor(
    private inlineTemplates: Map<string, Template>,  // 当前文件的内联模板
    private globalRegistry: TemplateRegistry         // 全局模板注册表
  ) {
    logger.debug('TemplateResolver 创建', {
      inlineTemplateCount: this.inlineTemplates.size,
      globalTemplateCount: this.globalRegistry.getAllTemplates().length
    });
  }

  /**
   * 解析模板（按优先级）
   */
  resolveTemplate(templateId: string): TemplateResolutionResult {
    // 优先级1：内联模板
    const inlineTemplate = this.inlineTemplates.get(templateId);
    if (inlineTemplate) {
      logger.debug('使用内联模板', { templateId });
      return {
        template: this.convertToTemplateDefinition(inlineTemplate),
        source: 'inline',
        templateId
      };
    }

    // 优先级2：全局注册模板
    const globalTemplate = this.globalRegistry.getTemplate(templateId);
    if (globalTemplate) {
      logger.debug('使用全局模板', { templateId });
      return {
        template: globalTemplate,
        source: 'global',
        templateId
      };
    }

    // 未找到模板
    logger.warn('模板未找到', { templateId });
    return {
      template: null,
      source: 'none',
      templateId
    };
  }

  /**
   * 验证模板参数
   */
  validateTemplateParameters(
    templateId: string, 
    parameters: Record<string, any>
  ): { isValid: boolean; errors: string[] } {
    const resolution = this.resolveTemplate(templateId);
    
    if (!resolution.template) {
      return {
        isValid: false,
        errors: [`模板不存在: ${templateId}`]
      };
    }

    // 对于内联模板，使用简化的验证逻辑
    if (resolution.source === 'inline') {
      return this.validateInlineTemplateParameters(resolution.template, parameters);
    }

    // 对于全局模板，使用现有的验证逻辑
    return this.globalRegistry.validateTemplateParameters(templateId, parameters);
  }

  /**
   * 获取模板信息（用于调试）
   */
  getTemplateInfo(templateId: string): any {
    const resolution = this.resolveTemplate(templateId);
    
    if (!resolution.template) {
      return null;
    }

    return {
      id: templateId,
      name: resolution.template.name,
      source: resolution.source,
      stepCount: resolution.template.steps?.length || 0,
      hasParameters: !!(resolution.template.parameters && resolution.template.parameters.length > 0),
      description: resolution.template.description
    };
  }

  /**
   * 列出所有可用模板
   */
  listAvailableTemplates(): Array<{ id: string; source: 'inline' | 'global'; name: string }> {
    const templates: Array<{ id: string; source: 'inline' | 'global'; name: string }> = [];

    // 内联模板
    for (const [id, template] of this.inlineTemplates) {
      templates.push({
        id,
        source: 'inline',
        name: template.name
      });
    }

    // 全局模板（排除与内联模板同名的）
    for (const globalTemplate of this.globalRegistry.getAllTemplates()) {
      if (!this.inlineTemplates.has(globalTemplate.id)) {
        templates.push({
          id: globalTemplate.id,
          source: 'global',
          name: globalTemplate.name
        });
      }
    }

    return templates;
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const availableTemplates = this.listAvailableTemplates();
    const inlineCount = availableTemplates.filter(t => t.source === 'inline').length;
    const globalCount = availableTemplates.filter(t => t.source === 'global').length;

    return {
      totalAvailable: availableTemplates.length,
      inlineTemplates: inlineCount,
      globalTemplates: globalCount,
      hasConflicts: this.detectNameConflicts()
    };
  }

  /**
   * 检测模板名称冲突
   */
  private detectNameConflicts(): string[] {
    const conflicts: string[] = [];
    
    for (const inlineId of this.inlineTemplates.keys()) {
      if (this.globalRegistry.getTemplate(inlineId)) {
        conflicts.push(inlineId);
      }
    }

    return conflicts;
  }

  /**
   * 将内联模板转换为TemplateDefinition格式
   */
  private convertToTemplateDefinition(template: Template): TemplateDefinition {
    return {
      id: template.name,
      name: template.name,
      type: template.type === 'shared' ? 'shared' : 'business',
      description: template.description,
      parameters: template.parameters?.map(param => ({
        name: param.name || '',
        type: param.type || 'string',
        required: param.required || false,
        description: param.description,
        default: param.default
      })) || [],
      steps: template.steps || [],
      metadata: {
        filePath: '<inline>',
        lastModified: new Date().toISOString()
      }
    };
  }

  /**
   * 验证内联模板参数（简化版）
   */
  private validateInlineTemplateParameters(
    template: TemplateDefinition, 
    parameters: Record<string, any>
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需参数
    for (const param of template.parameters || []) {
      if (param.required && !(param.name in parameters)) {
        errors.push(`缺少必需参数: ${param.name}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
