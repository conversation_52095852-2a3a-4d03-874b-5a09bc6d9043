/**
 * 基础设施层导出
 * 提供统一的资源管理、执行器管理和验证服务
 */

// ResourceService - 统一资源管理
export {
  ResourceService,
  ResourceServiceFactory,
  createResourceService,
  createResourceServiceWithCDP,
  createResourceServiceWithNewBrowser,
  createResourceServiceSmart,
} from './resource-service';

export type {
  IResourceService,
  BrowserConnectionConfig,
  ResourceStatus,
  PageCreateOptions,
  ResourceCleanupOptions,
} from './resource-service';

// ExecutorService - 统一执行器管理
export {
  ExecutorService,
  ExecutorServiceFactory,
  DefaultExecutorSelectionStrategy,
  createExecutorService,
  createSmartExecutorService,
} from './executor-service';

export type {
  IExecutorService,
  ExecutorConfig,
  ExecutorRegistration,
  ExecutorStatus,
  ExecutorServiceStatus,
  ExecutorSelectionStrategy,
} from './executor-service';
