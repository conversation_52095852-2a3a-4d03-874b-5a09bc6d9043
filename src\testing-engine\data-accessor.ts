import { logger } from '../utils/logger';
import { getNestedProperty } from '../utils/helpers';

/**
 * 简化的数据访问器
 * 支持变量解析和配置访问，使用 config.* 命名空间
 */
export class DataAccessor {
  private variables: Record<string, any> = {};
  private config: Record<string, any> = {};

  constructor(initialVariables: Record<string, any> = {}, config: Record<string, any> = {}) {
    this.variables = { ...initialVariables };
    this.config = { ...config };

    logger.debug('数据访问器初始化', {
      variableCount: Object.keys(this.variables).length,
      variables: Object.keys(this.variables),
      configKeys: Object.keys(this.config),
      hasConfig: Object.keys(this.config).length > 0
    });
  }
  
  /**
   * 获取变量值
   * 支持 config.* 命名空间访问配置
   */
  get(key: string): any {
    // 检查是否是配置访问
    if (key.startsWith('config.')) {
      const configKey = key.substring(7); // 移除 'config.' 前缀
      const value = getNestedProperty(this.config, configKey);
      logger.debug(`获取配置变量: ${key} = ${value}`);
      return value;
    }

    // 普通变量访问
    const value = getNestedProperty(this.variables, key);
    logger.debug(`获取变量: ${key} = ${value}`);
    return value;
  }
  
  /**
   * 设置变量值
   * 注意：config.* 命名空间的变量是只读的，不能修改
   */
  set(key: string, value: any): void {
    if (key.startsWith('config.')) {
      logger.warn(`尝试修改只读配置变量: ${key}，操作被忽略`);
      return;
    }

    this.variables[key] = value;
    logger.debug(`设置变量: ${key} = ${value}`);
  }
  
  /**
   * 批量设置变量
   */
  setAll(variables: Record<string, any>): void {
    this.variables = { ...this.variables, ...variables };
    logger.debug('批量设置变量', {
      newVariables: Object.keys(variables),
      totalVariables: Object.keys(this.variables).length
    });
  }


  
  /**
   * 设置数据源数据
   */
  setDataSourceData(name: string, data: any): void {
    this.set(name, data);
    logger.info(`设置数据源数据: ${name}`, {
      dataType: Array.isArray(data) ? 'array' : typeof data,
      dataCount: Array.isArray(data) ? data.length : 'N/A'
    });
  }
  
  /**
   * 解析字符串中的变量引用
   * 支持 {{variable}} 语法
   */
  resolve(expression: string): any {
    if (typeof expression !== 'string') {
      return expression;
    }
    
    return this.resolveString(expression);
  }
  
  /**
   * 解析对象中的所有变量引用
   * @param obj 要解析的对象
   * @param templateParameters 模板参数（可选）
   */
  resolveObject(obj: any, templateParameters?: Record<string, any>): any {
    return this.resolveObjectVariables(obj, templateParameters);
  }
  
  /**
   * 获取所有变量（调试用）
   */
  getAllVariables(): Record<string, any> {
    return { ...this.variables };
  }

  /**
   * 获取所有配置（调试用）
   */
  getAllConfig(): Record<string, any> {
    return { ...this.config };
  }

  /**
   * 获取所有可用的配置键（用于文档和调试）
   */
  getAvailableConfigKeys(): string[] {
    const flattenKeys = (obj: any, prefix = ''): string[] => {
      const keys: string[] = [];
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        keys.push(`config.${fullKey}`);
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          keys.push(...flattenKeys(value, fullKey));
        }
      }
      return keys;
    };

    return flattenKeys(this.config);
  }
  
  /**
   * 清空所有变量
   */
  clear(): void {
    this.variables = {};
    logger.debug('清空所有变量');
  }
  
  /**
   * 解析字符串中的变量引用
   * 支持 {{variable}} 语法和模板参数
   */
  private resolveString(text: string, templateParameters?: Record<string, any>): any {
    if (typeof text !== 'string') {
      return text;
    }

    // 检查是否是纯变量引用（整个字符串就是一个变量）
    const pureVariableMatch = text.match(/^\{\{\s*([^}]+)\s*\}\}$/);
    if (pureVariableMatch) {
      const trimmedPath = pureVariableMatch[1].trim();

      // 对于纯变量引用，返回原始值（保持类型）
      // 1. 先查找模板参数（最高优先级）
      if (templateParameters) {
        const value = getNestedProperty(templateParameters, trimmedPath);
        if (value !== undefined) {
          return value; // 保持原始类型
        }
      }

      // 2. 查找用户定义的变量
      if (!trimmedPath.startsWith('config.')) {
        const userValue = getNestedProperty(this.variables, trimmedPath);
        if (userValue !== undefined) {
          return userValue; // 保持原始类型
        }
      }

      // 3. 查找配置变量（最低优先级）
      if (trimmedPath.startsWith('config.')) {
        const configKey = trimmedPath.substring(7);
        const configValue = getNestedProperty(this.config, configKey);
        if (configValue !== undefined) {
          return configValue; // 保持原始类型
        }
      }

      // 如果变量未找到，根据上下文决定处理方式
      return undefined;
    }

    // 对于包含变量的字符串模板，进行字符串替换
    return text.replace(/\{\{\s*([^}]+)\s*\}\}/g, (match, variablePath) => {
      const trimmedPath = variablePath.trim();

      // 1. 先查找模板参数（最高优先级）
      if (templateParameters) {
        const value = getNestedProperty(templateParameters, trimmedPath);
        if (value !== undefined) {
          return this.valueToString(value);
        }
      }

      // 2. 查找用户定义的变量
      if (!trimmedPath.startsWith('config.')) {
        const userValue = getNestedProperty(this.variables, trimmedPath);
        if (userValue !== undefined) {
          return this.valueToString(userValue);
        }
      }

      // 3. 查找配置变量（最低优先级）
      if (trimmedPath.startsWith('config.')) {
        const configKey = trimmedPath.substring(7);
        const configValue = getNestedProperty(this.config, configKey);
        if (configValue !== undefined) {
          return this.valueToString(configValue);
        }
      }

      return this.valueToString(undefined);
    });
  }

  /**
   * 将值转换为字符串，对复杂对象使用 JSON 序列化
   */
  private valueToString(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    }

    // 对于数组和对象，使用 JSON 序列化
    try {
      return JSON.stringify(value);
    } catch (error) {
      logger.warn('值序列化失败，使用默认字符串转换', { value, error });
      return String(value);
    }
  }
  
  /**
   * 处理未定义的变量，根据上下文决定返回值
   */
  private handleUndefinedVariable(originalText: string, variablePath: string, templateParameters?: Record<string, any>): any {
    const isTemplateContext = templateParameters !== undefined;

    if (isTemplateContext) {
      // 模板上下文：返回 undefined，让条件判断正常工作
      logger.debug(`模板变量未定义: ${variablePath}`);
      return undefined;
    } else {
      // 普通上下文：保持现有行为
      logger.warn(`变量引用 ${originalText} 无法解析，保留原始格式`);
      return originalText;
    }
  }



  /**
   * 递归解析对象中的所有变量引用
   */
  private resolveObjectVariables(obj: any, templateParameters?: Record<string, any>): any {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (typeof obj === 'string') {
      return this.resolveString(obj, templateParameters);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveObjectVariables(item, templateParameters));
    }
    
    if (typeof obj === 'object') {
      const resolved: any = {};
      for (const [key, value] of Object.entries(obj)) {
        resolved[key] = this.resolveObjectVariables(value, templateParameters);
      }
      return resolved;
    }
    
    return obj;
  }
}
