import { logger } from '../utils/logger';
import { ConfigService } from './config-service';
import { ExecutionEngine, type ExecutionContext } from './execution-engine';
import { DataAccessor } from './data-accessor';
import type {
  ResolvedConfig,
  TestResult,
  StepResult,
  StepData
} from './types';

// 导入解析器
import { DSLParser } from '../dsl/parser';

// 导入新的基础设施服务
import { ResourceServiceFactory } from '../infrastructure/resource-service';
import type { IResourceService, BrowserConnectionConfig } from '../infrastructure/resource-service';
import { ExecutorServiceFactory } from '../infrastructure/executor-service';
import type { IExecutorService, ExecutorConfig } from '../infrastructure/executor-service';

// 导入模板系统
import { TemplateFactory } from '../template/template-loader';
import { TemplateResolver } from '../template/template-resolver';

// 导入脚本系统
import { ScriptManager } from '../script/script-manager';

// 导入新的架构组件
import { APIService } from './api-service';
import { TaskManager } from './task-manager';

/**
 * TestService 接口定义
 * 统一业务逻辑处理，整合 TestRunner 和 GlobalTestTaskManager 的功能
 */
export interface ITestService {
  /**
   * 初始化服务
   */
  initialize(): Promise<void>;
  
  /**
   * 执行 YAML 内容
   * 统一处理 hooks 和生命周期
   */
  executeYamlContent(yamlContent: string, options?: any): Promise<TestResult>;
  
  /**
   * 执行单个步骤
   * 统一处理页面调用和后台调用，支持页面上下文传递
   */
  executeStep(stepData: StepData, options?: any): Promise<StepResult>;
  
  /**
   * 执行模板
   * 统一处理模板加载和执行
   */
  executeTemplate(templateId: string, parameters?: Record<string, any>): Promise<StepResult>;
  
  /**
   * 清理资源
   */
  cleanup(options?: { keepBrowser?: boolean }): Promise<void>;
  
  /**
   * 获取服务状态
   */
  getStatus(): ServiceStatus;

  /**
   * 设置 TestingEngine 到 APIService (解决循环依赖)
   */
  setTestingEngineToAPIService(testingEngine: any): void;
}

/**
 * 服务状态接口
 */
export interface ServiceStatus {
  initialized: boolean;
  resourceManager: 'ready' | 'not ready' | 'error';
  taskManager: 'ready' | 'not ready' | 'error';
  executorManager: 'ready' | 'not ready' | 'error';
  activeConnections: number;
  runningTasks: number;
}



/**
 * TestService 实现类
 * 统一业务逻辑处理，解决当前架构中的不一致问题
 */
export class TestService implements ITestService {
  private configService: ConfigService;
  private isInitialized = false;

  // 资源管理
  private resourceService: IResourceService | null = null;
  private executorService: IExecutorService | null = null;
  private templateFactory: TemplateFactory | null = null;
  private scriptManager: ScriptManager | null = null;
  private executionEngine: ExecutionEngine | null = null;

  // 新架构组件
  private apiService: APIService | null = null;
  private newTaskManager: TaskManager | null = null;

  // 状态跟踪
  private activeConnections = 0;
  private runningTasks = 0;

  constructor(configService: ConfigService) {
    this.configService = configService;

    logger.info('TestService 创建完成', {
      config: this.configService.getBaseConfig()
    });
  }
  
  /**
   * 初始化服务
   * 统一初始化所有必要的组件
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    
    logger.info('开始初始化 TestService');
    
    try {
      const baseConfig = this.configService.getBaseConfig();
      
      // 1. 初始化资源管理器
      await this.initializeResourceManager(baseConfig);

      // 2. 初始化执行器服务
      await this.initializeExecutorService(baseConfig);

      // 4. 初始化模板系统
      await this.initializeTemplateSystem(baseConfig);

      // 5. 初始化脚本系统
      await this.initializeScriptSystem(baseConfig);

      // 6. 初始化执行引擎
      this.initializeExecutionEngine();

      // 6. 初始化新架构组件并建立连接
      await this.initializeNewArchitectureComponents();

      this.isInitialized = true;
      
      logger.info('TestService 初始化完成', {
        connectionType: baseConfig.connectionType,
        executorType: baseConfig.executorType
      });
      
    } catch (error) {
      logger.error('TestService 初始化失败', {
        error: error instanceof Error ? error.message : String(error),
      });
      
      // 清理失败的初始化
      await this.cleanup();
      throw error;
    }
  }
  
  /**
   * 执行 YAML 内容
   * 统一处理 hooks 和生命周期，解决 TestRunner 和 GlobalTestTaskManager 不一致的问题
   */
  async executeYamlContent(yamlContent: string, options: any = {}): Promise<TestResult> {
    await this.ensureInitialized();

    logger.info('开始执行 YAML 内容', { options });

    try {
      // 统一验证和解析YAML（移除重复验证，遵循KISS原则）
      const parseResult = DSLParser.parseWithValidation(yamlContent);

      if (!parseResult.isValid) {
        const errorMessages = parseResult.errors.map(e => e.message).join('; ');
        throw new Error(`YAML 验证失败: ${errorMessages}`);
      }

      const testSuite = parseResult.parsed!;

      // 确保 testSuite 有 config 属性
      if (!testSuite.config) {
        throw new Error('YAML 内容缺少 config 配置');
      }

      // 使用配置服务解析和验证配置
      const configResult = await this.configService.resolveAndValidateConfig(
        testSuite.config,
        options
      );

      if (!configResult.validation.isValid) {
        const configErrors = configResult.validation.errors.map((e: any) => e.message).join('; ');
        logger.warn('配置验证有警告', { errors: configErrors });
      }

      // 创建执行上下文（传递内联模板）
      const context = await this.createExecutionContext(
        configResult.config,
        testSuite.config.variables,
        testSuite.inlineTemplates
      );

      // 使用ExecutionEngine执行测试套件
      if (!this.executionEngine) {
        throw new Error('执行引擎未初始化');
      }

      return await this.executionEngine.executeTestSuite(testSuite, context);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('YAML 内容执行失败', { error: errorMessage });
      
      return {
        success: false,
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          skipped: 0,
          duration: 0,
        },
        results: [],
        errors: [errorMessage],
      };
    }
  }
  
  /**
   * 执行单个步骤
   * 统一处理页面调用和后台调用的逻辑
   */
  async executeStep(stepData: StepData, options: any = {}): Promise<StepResult> {
    await this.ensureInitialized();

    const startTime = Date.now();
    logger.info('开始执行单步测试', { stepData, options });

    try {
      if (!this.executionEngine) {
        throw new Error('执行引擎未初始化');
      }

      // 标准化步骤数据
      const normalizedStepData = this.normalizeStepData(stepData);

      // 使用DSLParser验证步骤
      const stepValidation = DSLParser.validateStep(normalizedStepData as any);
      if (!stepValidation.isValid) {
        const stepErrors = stepValidation.errors.map((e: any) => e.message).join('; ');
        logger.warn('步骤验证有警告', { errors: stepErrors });
      }

      // 创建完整的执行上下文
      const executionContext = await this.createExecutionContext(
        this.configService.resolveConfig(),
        {} // 空变量，单步执行通常不需要预设变量
      );

      // 如果有页面上下文，优先使用传递的页面
      if (options.pageContext?.page) {
        executionContext.page = options.pageContext.page;
        executionContext.pageId = options.pageContext.pageId;
        logger.debug('使用页面上下文中的页面', {
          pageId: options.pageContext.pageId,
          pageUrl: options.pageContext.url
        });
      }

      // 使用ExecutorService直接执行，获得完整的系统支持
      await this.executorService!.executeStep(
        normalizedStepData as any,
        executionContext,
        options.executorType || this.configService.getBaseConfig().executorType
      );

      const duration = Date.now() - startTime;

      logger.info('单步测试执行完成', {
        success: true,
        duration,
        stepAction: stepData.action
      });

      return {
        success: true,
        duration,
        error: undefined,
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('单步测试执行失败', { 
        error: errorMessage, 
        duration,
        stepAction: stepData.action 
      });
      
      return {
        success: false,
        duration,
        error: errorMessage,
      };
    }
  }
  
  /**
   * 执行模板
   * 统一处理模板加载和执行
   */
  async executeTemplate(templateId: string, parameters: Record<string, any> = {}): Promise<StepResult> {
    await this.ensureInitialized();

    const startTime = Date.now();
    logger.info('开始执行模板测试', { templateId, parameters });

    try {
      if (!this.executionEngine) {
        throw new Error('执行引擎未初始化');
      }

      // 创建执行上下文（用于模板执行）
      const context = await this.createExecutionContext(
        this.configService.getBaseConfig(),
        parameters
      );

      // 使用ExecutionEngine执行模板测试
      const result = await this.executionEngine.executeTemplate(templateId, parameters, context);

      const duration = Date.now() - startTime;

      logger.info('模板测试执行完成', {
        success: result.success,
        duration,
        templateId
      });

      return {
        success: result.success,
        duration,
        error: result.error,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('模板测试执行失败', {
        error: errorMessage,
        duration,
        templateId
      });

      return {
        success: false,
        duration,
        error: errorMessage,
      };
    }
  }
  
  /**
   * 清理资源
   */
  async cleanup(options: { keepBrowser?: boolean } = {}): Promise<void> {
    logger.info('开始清理 TestService 资源', options);
    
    try {
      // 清理执行器服务
      if (this.executorService) {
        await this.executorService.cleanup();
        this.executorService = null;
      }
      
      // 清理新架构组件中的任务
      if (this.newTaskManager) {
        // 取消所有运行中的任务
        const runningTasks = this.newTaskManager.getRunningTests();
        for (const task of runningTasks) {
          await this.newTaskManager.cancelTest(task.id);
        }
      }

      // 清理模板系统
      if (this.templateFactory) {
        this.templateFactory.dispose();
        this.templateFactory = null;
      }

      // 清理脚本系统
      if (this.scriptManager) {
        // ScriptManager 目前没有 dispose 方法，可以直接设为 null
        this.scriptManager = null;
      }

      // 清理配置服务
      if (this.configService) {
        this.configService.dispose();
      }

      // 清理执行引擎
      this.cleanupExecutionEngine();

      // 清理资源服务
      if (this.resourceService) {
        if (options.keepBrowser) {
          await this.resourceService.cleanupKeepBrowser();
        } else {
          await this.resourceService.cleanup();
          this.resourceService = null;
        }
      }
      
      // 重置状态
      if (!options.keepBrowser) {
        this.isInitialized = false;
      }
      
      this.activeConnections = 0;
      this.runningTasks = 0;
      
      logger.info('TestService 资源清理完成');
      
    } catch (error) {
      logger.warn('TestService 清理过程中出现错误', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
  
  /**
   * 获取服务状态
   */
  getStatus(): ServiceStatus {
    return {
      initialized: this.isInitialized,
      resourceManager: this.resourceService ? 'ready' : 'not ready',
      taskManager: this.newTaskManager ? 'ready' : 'not ready',
      executorManager: this.executorService ? 'ready' : 'not ready',
      activeConnections: this.activeConnections,
      runningTasks: this.runningTasks,
    };
  }

  /**
   * 设置 TestingEngine 到 APIService (解决循环依赖)
   */
  setTestingEngineToAPIService(testingEngine: any): void {
    if (this.apiService && 'setTestingEngine' in this.apiService) {
      (this.apiService as any).setTestingEngine(testingEngine);
      logger.debug('TestingEngine 已注入到 APIService');
    }
  }
  
  // === 私有方法 ===
  
  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }
  
  /**
   * 标准化步骤数据
   */
  private normalizeStepData(stepData: StepData): StepData {
    return {
      ...stepData,
      id: stepData.id || `step-${Date.now()}`,
      name: stepData.name || `执行 ${stepData.action}`,
    };
  }

  /**
   * 初始化资源服务
   */
  private async initializeResourceManager(config: any): Promise<void> {
    logger.debug('初始化资源服务', { connectionType: config.connectionType });

    const browserConfig: BrowserConnectionConfig = {
      connectionType: config.connectionType,
      browser: config.browser || 'chromium',
      headless: config.headless,
      slowMo: config.slowMo,
      debuggingPort: config.debuggingPort,
      cdpEndpoint: config.cdpEndpoint,
    };

    // 使用ResourceServiceFactory创建服务，自动处理回退逻辑
    this.resourceService = await ResourceServiceFactory.create(browserConfig);

    logger.info('资源服务初始化成功', {
      connectionType: config.connectionType,
      status: this.resourceService.getStatus()
    });
  }



  /**
   * 初始化执行器管理器
   */
  private async initializeExecutorService(config: any): Promise<void> {
    logger.debug('初始化执行器服务', { executorType: config.executorType });

    const executorConfig: ExecutorConfig = {
      type: config.executorType || 'web',
    };

    this.executorService = await ExecutorServiceFactory.create(executorConfig);
    logger.info('执行器服务初始化成功');
  }

  private async initializeTemplateSystem(config: any): Promise<void> {
    logger.debug('初始化模板系统', { templateDirs: config.templateDirs });

    try {
      // 创建模板工厂，配置模板目录
      this.templateFactory = new TemplateFactory({
        templateDirs: config.templateDirs || ['./templates'],
        patterns: ['**/*.yml', '**/*.yaml'],
        autoScan: false, // 手动调用初始化
        cacheEnabled: true,
      });

      // 初始化模板系统（扫描并注册模板）
      await this.templateFactory.initialize();

      const loadedTemplates = this.templateFactory.listTemplates();

      logger.info('模板系统初始化成功', {
        templateDirs: config.templateDirs,
        loadedTemplates: loadedTemplates.length,
        templates: loadedTemplates.map(t => t.id)
      });

    } catch (error) {
      logger.error('模板系统初始化失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  private async initializeScriptSystem(config: any): Promise<void> {
    logger.debug('初始化脚本系统', { scriptDirs: config.scriptDirs });

    try {
      // 创建脚本管理器
      this.scriptManager = new ScriptManager();

      // 获取脚本目录，使用 PathResolver 提供的默认路径
      const scriptDirs = config.scriptDirs || [this.scriptManager.getPathResolver().getScriptsDir()];

      // 初始化脚本系统（扫描并注册脚本）
      const loadResult = await this.scriptManager.initialize(scriptDirs);

      logger.info('脚本系统初始化成功', {
        scriptDirs,
        loadedScripts: loadResult.scripts.length,
        scripts: this.scriptManager.getScriptNames(),
        success: loadResult.success,
        errors: loadResult.errors.length
      });

      if (loadResult.errors.length > 0) {
        logger.warn('脚本加载过程中出现错误', { errors: loadResult.errors });
      }

    } catch (error) {
      logger.error('脚本系统初始化失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 初始化执行引擎
   */
  private initializeExecutionEngine(): void {
    if (!this.executorService) {
      throw new Error('执行器服务未初始化，无法创建执行引擎');
    }

    this.executionEngine = new ExecutionEngine(this.executorService);
    logger.debug('执行引擎初始化完成');
  }

  /**
   * 初始化新架构组件并建立连接
   */
  private async initializeNewArchitectureComponents(): Promise<void> {
    logger.debug('初始化新架构组件');

    try {
      // 1. 创建TaskManager
      if (!this.newTaskManager) {
        this.newTaskManager = new TaskManager();
        logger.debug('TaskManager 创建完成');
      }

      // 2. 创建APIService (延迟注入 TestingEngine)
      if (!this.apiService && this.newTaskManager) {
        // 暂时传入 null，稍后通过 setTestingEngine 注入
        this.apiService = new APIService(null as any, this.newTaskManager);
        logger.debug('APIService 创建完成 (待注入 TestingEngine)');
      }

      // 3. 将APIService设置到ResourceService
      if (this.resourceService && this.apiService) {
        await this.resourceService.setAPIService(this.apiService);
        logger.info('APIService已设置到ResourceService，页面事件监听已启动');
      }

      logger.info('新架构组件初始化完成');

    } catch (error) {
      logger.error('新架构组件初始化失败', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }



  /**
   * 创建执行上下文
   * 现在支持配置变量访问和内联模板解析
   */
  private async createExecutionContext(
    config: ResolvedConfig,
    variables: Record<string, any> = {},
    inlineTemplates?: Map<string, any>
  ): Promise<ExecutionContext> {
    // 传递配置到 DataAccessor，支持 config.* 变量访问
    const dataAccessor = new DataAccessor(variables, config);

    logger.debug('创建执行上下文', {
      variableCount: Object.keys(variables).length,
      configKeys: Object.keys(config),
      availableConfigKeys: dataAccessor.getAvailableConfigKeys().slice(0, 10) // 显示前10个配置键
    });

    // 创建模板解析器
    const templateResolver = this.createTemplateResolver(inlineTemplates);

    const context: ExecutionContext = {
      config,
      dataAccessor,
      resourceService: this.resourceService || undefined,
      templateResolver,
      scriptManager: this.scriptManager || undefined,
    };

    // 如果有页面，添加页面信息
    if (this.resourceService) {
      const page = await this.resourceService.getPage();
      if (page) {
        context.page = page;
        context.pageId = page.url();
      }
    }

    logger.debug('执行上下文创建完成', {
      hasResourceService: !!context.resourceService,
      hasPage: !!context.page,
      variableCount: Object.keys(variables).length
    });

    return context;
  }

  /**
   * 创建模板解析器
   */
  private createTemplateResolver(inlineTemplates?: Map<string, any>): TemplateResolver {
    const templates = inlineTemplates || new Map();
    const globalRegistry = this.templateFactory?.getRegistry();

    if (!globalRegistry) {
      logger.warn('全局模板注册表未初始化，只能使用内联模板');
      // 创建空的注册表作为fallback
      const { TemplateRegistry } = require('../template/template-registry');
      return new TemplateResolver(templates, new TemplateRegistry());
    }

    return new TemplateResolver(templates, globalRegistry);
  }

  /**
   * 清理执行引擎
   */
  private cleanupExecutionEngine(): void {
    if (this.executionEngine) {
      this.executionEngine.dispose();
      this.executionEngine = null;
    }
  }
}
