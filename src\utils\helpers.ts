import { randomUUID } from 'node:crypto';

/**
 * Generate a unique identifier
 */
export function generateId(prefix?: string): string {
  const id = randomUUID().slice(0, 8);
  return prefix ? `${prefix}-${id}` : id;
}

/**
 * Sleep for specified milliseconds
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Retry a function with exponential backoff
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: {
    maxAttempts?: number;
    delay?: number;
    backoff?: number;
    condition?: (error: Error) => boolean;
  } = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = 2,
    condition = () => true,
  } = options;

  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxAttempts || !condition(lastError)) {
        throw lastError;
      }

      const waitTime = delay * backoff ** (attempt - 1);
      await sleep(waitTime);
    }
  }

  throw lastError!;
}

/**
 * Deep merge objects
 */
export function deepMerge<T = any>(target: any, ...sources: any[]): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key], source[key]);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
}

/**
 * Check if value is an object
 */
export function isObject(item: any): boolean {
  return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as any;
  if (Array.isArray(obj)) return obj.map(deepClone) as any;
  if (typeof obj === 'object') {
    const cloned = {} as any;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  return obj;
}

/**
 * Get nested object property safely
 */
export function getNestedProperty(obj: any, path: string, defaultValue?: any): any {
  const keys = path.split('.');
  let result = obj;

  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return defaultValue;
    }
  }

  return result;
}

/**
 * Set nested object property
 */
export function setNestedProperty(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
}

/**
 * Format duration in human readable format
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }
  if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`;
  }
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(0);
    return `${minutes}m ${seconds}s`;
}

/**
 * Parse template variables
 */
export function parseTemplate(template: string, variables: Record<string, any>): string {
  return template.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
    const value = getNestedProperty(variables, key.trim());
    return value !== undefined ? String(value) : match;
  });
}

/**
 * Validate required fields in object
 */
export function validateRequired(obj: any, fields: string[], name = 'Object'): void {
  const missing = fields.filter((field) => !getNestedProperty(obj, field));
  if (missing.length > 0) {
    throw new Error(`${name} is missing required fields: ${missing.join(', ')}`);
  }
}

/**
 * Sanitize filename for safe file system operations
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-z0-9]/gi, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .toLowerCase();
}

/**
 * Create timestamp string
 */
export function createTimestamp(format: 'iso' | 'filename' = 'iso'): string {
  const now = new Date();
  
  if (format === 'filename') {
    return now.toISOString()
      .replace(/:/g, '-')
      .replace(/\./g, '-')
      .slice(0, -5);
  }
  
  return now.toISOString();
}

/**
 * Check if running in CI environment
 */
export function isCI(): boolean {
  return !!(
    process.env.CI ||
    process.env.CONTINUOUS_INTEGRATION ||
    process.env.BUILD_NUMBER ||
    process.env.GITHUB_ACTIONS ||
    process.env.GITLAB_CI ||
    process.env.JENKINS_URL
  );
}

/**
 * 包装脚本为 IIFE (Immediately Invoked Function Expression)
 * 支持异步 async/await 写法
 *
 * @param script - 要包装的脚本代码
 * @param context - 可选的上下文变量，会被注入到脚本中
 * @returns 包装后的可执行函数
 */
export function wrapScriptAsIIFE(script: string, context?: Record<string, any>): Function {
  // 检测脚本是否包含 await 关键字，决定是否需要 async
  const isAsync = /\bawait\b/.test(script);

  // 构建上下文变量注入代码
  let contextInjection = '';
  if (context && Object.keys(context).length > 0) {
    const contextEntries = Object.entries(context)
      .map(([key, value]) => `const ${key} = ${JSON.stringify(value)};`)
      .join('\n  ');
    contextInjection = `\n  // 注入上下文变量\n  ${contextEntries}\n`;
  }

  // 构建 IIFE 包装器
  const asyncKeyword = isAsync ? 'async ' : '';
  const wrappedScript = `
(${asyncKeyword}function() {${contextInjection}
  // 用户脚本开始
  ${script}
  // 用户脚本结束
})();
`.trim();

  // 使用 new Function 创建可执行函数
  return new Function(`return ${wrappedScript}`);
}

/**
 * 执行包装后的脚本
 *
 * @param script - 要执行的脚本代码
 * @param context - 可选的上下文变量
 * @returns 脚本执行结果
 */
export async function executeWrappedScript(script: string, context?: Record<string, any>): Promise<any> {
  try {
    const wrappedFunction = wrapScriptAsIIFE(script, context);
    const result = wrappedFunction();

    // 如果结果是 Promise，等待其完成
    if (result && typeof result.then === 'function') {
      return await result;
    }

    return result;
  } catch (error) {
    throw new Error(`脚本执行失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 为 Playwright page.evaluate 准备脚本
 * 将脚本包装为可在浏览器中执行的 IIFE
 *
 * @param script - 要执行的脚本代码
 * @param context - 可选的上下文变量
 * @returns 准备好的脚本字符串
 */
export function prepareScriptForEvaluate(script: string, context?: Record<string, any>): string {
  // 检测脚本是否包含 await 关键字
  const isAsync = /\bawait\b/.test(script);

  // 构建上下文变量注入代码
  let contextInjection = '';
  if (context && Object.keys(context).length > 0) {
    const contextEntries = Object.entries(context)
      .map(([key, value]) => `const ${key} = ${JSON.stringify(value)};`)
      .join('\n  ');
    contextInjection = `\n  // 注入上下文变量\n  ${contextEntries}\n`;
  }

  // 构建 IIFE 包装器
  const asyncKeyword = isAsync ? 'async ' : '';
  return `
(${asyncKeyword}function() {${contextInjection}
  // 用户脚本开始
  ${script}
  // 用户脚本结束
})();
`.trim();
}