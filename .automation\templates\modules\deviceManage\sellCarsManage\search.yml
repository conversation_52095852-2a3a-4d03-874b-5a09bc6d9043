config:
  name: 奶茶车管理搜索测试
  description: 测试奶茶车管理页面的搜索功能，包括填充搜索表单、触发搜索和清空操作
  timeout: 30000
  captureScreenshots: true

templates:
  searchFormTemplate:
    name: 搜索表单操作模板
    type: business
    description: 通用搜索表单填充、查询和重置功能模板
    parameters:
      - name: modelValue
        type: string
        required: false
        description: 型号选择值
      - name: searchText
        type: string
        required: false
        description: 搜索文本内容
    steps:
      # 等待页面完全加载
      - action: waitForLoadState
        timeout: 5000
      
      # 选择型号
      - action: selectOption
        role: combobox
        value: "{{modelValue}}"
        timeout: 3000
      
      # 填充搜索输入框
      - action: fill
        role: textbox
        roleOptions:
          name: "车辆名称/编码"
        value: "{{searchText}}"
        timeout: 3000
      
      # 点击查询按钮执行搜索
      - action: click
        role: button
        roleOptions:
          name: "查询"
        timeout: 3000
      
      # 等待搜索结果
      - action: wait
        data: 2000
      
      # 点击重置按钮清空表单
      - action: click
        role: button
        roleOptions:
          name: "重置"
        timeout: 3000
      
      # 验证表单已清空
      - action: verify
        type: element
        role: textbox
        roleOptions:
          name: "车辆名称/编码"
        assertion: value
        expected: ""

tests:
  - name: 测试型号搜索功能
    description: 测试选择型号进行搜索
    steps:
      - action: useTemplate
        template: searchFormTemplate
        parameters:
          modelValue: "XH001"
          searchText: ""
  
  - name: 测试车辆名称搜索功能
    description: 测试输入车辆名称进行搜索
    steps:
      - action: useTemplate
        template: searchFormTemplate
        parameters:
          modelValue: ""
          searchText: "sell022"
  
  - name: 测试组合搜索功能
    description: 测试同时使用型号和车辆名称进行搜索
    steps:
      - action: useTemplate
        template: searchFormTemplate
        parameters:
          modelValue: "XH001"
          searchText: "L0001"
  
  - name: 测试清空功能
    description: 测试搜索表单的重置清空功能
    steps:
      - action: useTemplate
        template: searchFormTemplate
        parameters:
          modelValue: ""
          searchText: "test0514"
