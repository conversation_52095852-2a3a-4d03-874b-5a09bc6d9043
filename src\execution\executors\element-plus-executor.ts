import type { TestStep } from '../../types';
import type { Page } from '@playwright/test';
import { WebExecutor } from './web-executor';
import { logger } from '../../utils/logger';

/**
 * Element Plus 组件特化执行器
 * 继承WebExecutor，专门处理Element Plus组件的特殊交互逻辑
 */
export class ElementPlusExecutor extends WebExecutor {
  
  /**
   * 重写selectOption方法，处理Element Plus的el-select组件
   */
  protected async executeSelectOption(step: TestStep, page: Page): Promise<void> {
      // 禁用placeholder的点击事件，解决 role 点击不生效问题，element select 找不到对应 role 的问题
    await page.evaluate(() => {
      const selectElements = document.querySelectorAll('.el-form-item:has(.el-select)');
       Array.from(selectElements).forEach(element => {
        const labelNode = element.querySelector('.el-form-item__label,.el-select__selected-item.el-select__placeholder.is-transparent');
        const inputNode = element.querySelector('.el-select__input');
        const label = labelNode?.textContent?.trim();
        const placeholder = element.querySelector('.el-select__selected-item.el-select__placeholder.is-transparent');
        if (label && inputNode && placeholder) {
          (placeholder as any).style.pointerEvents = 'none';
          if(!inputNode.getAttribute('aria-label')) inputNode.setAttribute('aria-label', label);
        } 
     });
    });

    await this.handleElementPlusSelect(step, page);
  }

  protected async executeSelectDate(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'selectDate');
    await element.evaluate((el: any, value: string) => {
      el.value = value;
      // 触发 input 事件来模拟用户输入
      const inputEvent = new Event('input', { bubbles: true });
      el.dispatchEvent(inputEvent);
      
      // 触发 change 事件来模拟值变更
      const changeEvent = new Event('change', { bubbles: true });
      el.dispatchEvent(changeEvent);
      el.dispatchEvent(new Event('blur'));
    }, step.value);
  }

  /**
   * 重写check/uncheck方法，处理Element Plus的checkbox/radio
   */
/*   protected async executeCheck(step: TestStep, page: Page): Promise<void> {
     // 使用基类的唯一性校验
    const element = await this.requireSingleElement(step, page, 'check');
    await element.evaluate((el: any) => {
      const checked = el.checked
      if(!checked) el.click();
    });
  }

  protected async executeUncheck(step: TestStep, page: Page): Promise<void> {
      // 使用基类的唯一性校验
    const element = await this.requireSingleElement(step, page, 'uncheck');
    await element.evaluate((el: any) => {
      const checked = el.checked
      if(checked) el.click();
    });
  } */


  /**
   * 处理Element Plus select组件的特殊逻辑
   */
  private async handleElementPlusSelect(step: TestStep, page: Page): Promise<void> {
    // 使用基类的唯一性校验获取元素
    const selectElement = await this.requireSingleElement(step, page, 'Element Plus selectOption');
    const value = step.value || step.data;

    logger.debug('Handling Element Plus select', {
      selector: step.selector,
      value,
      label: step.label,
      index: step.index
    });
  

    // 1. 点击select组件打开下拉框
    await selectElement.click({force: true});
    
    // 2. 根据不同的选择方式进行选择
    if (step.label) {
      // 根据标签文本选择
      await page.getByRole('option', { name: step.label as string }).click({force: true});
    } else if (step.index !== undefined) {
      // 根据索引选择
      const option = page.locator('.el-select-dropdown [role="option"]:not([aria-disabled="true"])').filter({ visible: true}).nth(Number(step.index));
      if(await option.count() === 0) {
        throw new Error(`Element Plus SelectOption index out of range, index: ${step.index}, options: ${await option.count()}`);
      }
      await option.click({ force: true });
    } else if (value !== undefined) {
      let locator = page.getByRole('option', { name: step.value as string });
      if(await locator.count() === 0) {
        locator = page.getByRole('listitem').filter({ hasText: step.value as string });
      }
      await locator.first().click({force: true});
    } else {
      throw new Error('Element Plus SelectOption requires value, label, or index');
    }

    // 4. 等待下拉框关闭
    await page.waitForSelector('.el-select-dropdown', { 
      state: 'hidden',
      timeout: 3000 
    }).catch(() => {
      // 忽略超时错误，有些情况下下拉框可能不会完全隐藏
      logger.debug('Select dropdown close timeout ignored');
    });

    logger.debug('Element Plus select completed');
  }

}
