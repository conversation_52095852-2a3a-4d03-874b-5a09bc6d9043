import { TemplateRegistry, type TemplateDefinition, type TemplateParameter } from './template-registry';


export interface ResolvedTemplate {
  id: string;
  name: string;
  steps: any[];
  resolvedParameters: Record<string, any>;
}

export interface UnresolvedTemplate {
  id: string;
  name: string;
  rawSteps: any[];
  parameters: TemplateParameter[];
}

export class TemplateLoader {
  private registry: TemplateRegistry;

  constructor(registry: TemplateRegistry) {
    this.registry = registry;
  }

  /**
   * 加载未解析的模板（新的简化方法）
   */
  async loadTemplate(templateId: string): Promise<UnresolvedTemplate> {
    const template = this.registry.getTemplate(templateId);
    if (!template) {
      throw new Error(`模板不存在: ${templateId}`);
    }

    return {
      id: template.id,
      name: template.name,
      rawSteps: template.steps,  // 保持原始状态，不解析
      parameters: template.parameters || []
    };
  }

  /**
   * 验证模板参数
   */
  validateTemplateParameters(template: UnresolvedTemplate, parameters: Record<string, any>): void {
    for (const param of template.parameters) {
      if (param.required && !(param.name in parameters)) {
        throw new Error(`模板 ${template.id} 缺少必需参数: ${param.name}`);
      }
    }
  }

  /**
   * 获取模板信息（不解析）
   */
  getTemplateInfo(templateId: string): TemplateDefinition | null {
    return this.registry.getTemplate(templateId) || null;
  }

  /**
   * 列出所有模板
   */
  listTemplates(): TemplateDefinition[] {
    return this.registry.getAllTemplates();
  }

}
 
/**
 * 模板工厂类（简化版）
 */
export class TemplateFactory {
  private registry: TemplateRegistry;
  private loader: TemplateLoader;

  constructor(registryConfig?: any) {
    // 禁用自动扫描，由 initialize() 方法统一调用
    this.registry = new TemplateRegistry({
      ...registryConfig,
      autoScan: false  // 防止重复扫描
    });
    this.loader = new TemplateLoader(this.registry);
  }

  /**
   * 初始化模板系统
   */
  async initialize(): Promise<void> {
    await this.registry.scanAndRegisterTemplates();
  }

  /**
   * 获取注册表
   */
  getRegistry(): TemplateRegistry {
    return this.registry;
  }

  /**
   * 获取加载器
   */
  getLoader(): TemplateLoader {
    return this.loader;
  }

  /**
   * 快速加载模板（返回未解析的模板）
   */
  async loadTemplate(templateId: string): Promise<UnresolvedTemplate> {
    return this.loader.loadTemplate(templateId);
  }

  /**
   * 列出可用模板
   */
  listTemplates(filter?: {
    module?: string;
    type?: string;
  }): TemplateDefinition[] {
    if (!filter) {
      return this.registry.getAllTemplates();
    }

    return this.registry.searchTemplates(filter);
  }

  /**
   * 列出共享模板
   */
  listSharedTemplates(): TemplateDefinition[] {
    return this.registry.getSharedTemplates();
  }

  /**
   * 列出业务模板
   */
  listBusinessTemplates(): TemplateDefinition[] {
    return this.registry.getBusinessTemplates();
  }

  /**
   * 获取模板信息
   */
  getTemplateInfo(templateId: string): TemplateDefinition | undefined {
    return this.registry.getTemplate(templateId);
  }

  /**
   * 验证模板参数
   */
  validateParameters(
    templateId: string,
    parameters: Record<string, any>,
  ): {
    isValid: boolean;
    errors: string[];
  } {
    return this.registry.validateTemplateParameters(templateId, parameters);
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.registry.dispose();
    // TemplateLoader 不再有缓存需要清理
  }
}
