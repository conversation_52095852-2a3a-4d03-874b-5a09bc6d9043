#!/usr/bin/env node

/**
 * 测试内联模板功能
 */

const { TestService } = require('./src/testing-engine/test-service');
const { ConfigService } = require('./src/testing-engine/config-service');

async function testInlineTemplates() {
  console.log('🚀 测试内联模板功能');
  
  try {
    // 创建测试服务
    const testService = new TestService(new ConfigService({
      executorType: 'web',
      headless: true,
      timeout: 10000,
    }));

    // 测试1：内联模板优先级测试
    const yamlWithInlineTemplate = `
config:
  name: "内联模板测试"
  testMode: "flow"
  executorType: "web"

templates:
  - name: "inline-click-template"
    description: "内联点击模板"
    type: "business"
    parameters:
      - name: "selector"
        type: "string"
        required: true
    steps:
      - action: "click"
        selector: "{{selector}}"

tests:
  - name: "测试内联模板"
    steps:
      - action: "useTemplate"
        template: "inline-click-template"
        parameters:
          selector: "body"
        description: "使用内联模板点击body"
`;

    console.log('📋 测试1: 执行包含内联模板的YAML...');

    // 执行测试1：内联模板
    const result1 = await testService.executeYamlContent(yamlWithInlineTemplate);

    console.log('✅ 测试1执行完成:', {
      success: result1.success,
      duration: result1.duration,
      testCount: result1.testResults?.length || 0
    });

    // 测试2：模板执行API（ID模式）
    console.log('\n📋 测试2: 执行模板ID模式...');

    try {
      const result2 = await testService.executeTemplate('inline-click-template', {
        selector: '.test-button'
      });

      console.log('✅ 测试2执行完成:', {
        success: result2.success,
        duration: result2.duration,
        source: result2.source
      });
    } catch (error) {
      console.log('⚠️ 测试2预期失败（模板不在全局注册表中）:', error.message);
    }

    // 测试3：验证TemplateResolver统计信息
    console.log('\n📋 测试3: 验证TemplateResolver统计...');

    // 这里我们需要通过某种方式访问TemplateResolver
    // 暂时跳过，因为它在内部
    console.log('⏭️ 测试3跳过（需要内部访问）');

    // 清理
    await testService.cleanup();
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testInlineTemplates().then(() => {
  console.log('🎉 内联模板测试完成');
  process.exit(0);
}).catch(error => {
  console.error('💥 测试异常:', error);
  process.exit(1);
});
