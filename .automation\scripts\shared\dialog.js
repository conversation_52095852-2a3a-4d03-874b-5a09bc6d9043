/**
 * 对话框操作脚本
 * 提供对话框确认、取消和关闭功能
 */

// Dialog 操作相关的常量
const DIALOG_CONSTANTS = {
  // 按钮文本
  CONFIRM: '确定',
  CANCEL: '取消',
  CLOSE: '关闭',
  CLOSE_DIALOG: '关闭此对话框',
  OK: 'OK',
  YES: '是',
  NO: '否',

  // 角色定义
  BUTTON_ROLE: 'button',
  DIALOG_ROLE: 'dialog',

  // 等待时间
  WAIT_TIMEOUT: 1000,
  SHORT_WAIT: 500,
  DIALOG_WAIT: 2000,
};

// 操作类型判断函数
function isConfirmOperation(operation) {
  const confirmOperations = ['confirm', 'confirmDialog', '确定', '确认', 'ok', DIALOG_CONSTANTS.CONFIRM, DIALOG_CONSTANTS.OK, DIALOG_CONSTANTS.YES];
  return confirmOperations.includes(operation);
}

function isCancelOperation(operation) {
  const cancelOperations = ['cancel', 'cancelDialog', '取消', '关闭', 'close', DIALOG_CONSTANTS.CANCEL, DIALOG_CONSTANTS.CLOSE];
  return cancelOperations.includes(operation);
}

// 获取按钮名称数组
function getConfirmButtonNames() {
  return [DIALOG_CONSTANTS.CONFIRM, DIALOG_CONSTANTS.OK, DIALOG_CONSTANTS.YES];
}

function getCancelButtonNames() {
  return [DIALOG_CONSTANTS.CANCEL, DIALOG_CONSTANTS.NO];
}

function getCloseButtonNames() {
  return [DIALOG_CONSTANTS.CLOSE, DIALOG_CONSTANTS.CLOSE_DIALOG];
}

export default async function dialogOperations(params, context) {
  const { actionType, actionName, buttonText, timeout = DIALOG_CONSTANTS.WAIT_TIMEOUT } = params;
  const { page, logger } = context;

  // 确定实际的操作类型（actionName 优先级更高）
  const operation = actionName || actionType;
  if (!operation) {
    return {
      success: false,
      message: '必须指定 actionType 或 actionName 参数'
    };
  }

  logger.info('开始对话框操作', { operation, buttonText, timeout });

  try {
    let result;

    // 根据操作类型执行相应的操作
    if (isConfirmOperation(operation)) {
      result = await performDialogOperation(page, getConfirmButtonNames(), buttonText, timeout);
    } else if (isCancelOperation(operation)) {
      result = await performDialogOperation(page, getCancelButtonNames(), buttonText, timeout);
    } else if (operation === 'closeDialog' || operation === 'close') {
      result = await performDialogOperation(page, getCloseButtonNames(), buttonText, timeout);
    } else {
      // 自定义操作，使用指定的按钮文本
      const customButtonNames = buttonText ? [buttonText] : [operation];
      result = await performDialogOperation(page, customButtonNames, buttonText, timeout);
    }

    const success = result === true || (typeof result === 'object' && result.success);
    const message = success ? `${operation} 操作成功` : `${operation} 操作失败`;

    logger.info('对话框操作完成', { operation, success });

    return {
      success,
      message,
      data: typeof result === 'object' ? result : undefined
    };

  } catch (error) {
    const errorMessage = error.message || '未知错误';
    logger.error('对话框操作失败', { operation, error: errorMessage });

    return {
      success: false,
      message: `${operation} 操作失败: ${errorMessage}`
    };
  }
}

// 通用对话框操作方法
async function performDialogOperation(page, buttonNames, customButtonText, timeout) {
  try {
    // 如果指定了自定义按钮文本，优先使用
    const searchButtonNames = customButtonText ? [customButtonText] : buttonNames;

    for (const buttonName of searchButtonNames) {
      try {
        const button = await page.getByRole(DIALOG_CONSTANTS.BUTTON_ROLE, { name: buttonName });

        if (await button.isVisible()) {
          // 检查是否是确认类操作，需要网络监听
          const isConfirmAction = getConfirmButtonNames().includes(buttonName) ||
                                 (customButtonText && isConfirmOperation(customButtonText));

          if (isConfirmAction) {
            return await performConfirmWithNetworkMonitoring(page, button, buttonName, timeout);
          } else {
            // 简单的点击操作
            await button.click();
            await page.waitForTimeout(timeout || DIALOG_CONSTANTS.SHORT_WAIT);

            return {
              success: true,
              buttonName: buttonName,
              clicked: true,
              method: 'simple_click'
            };
          }
        }
      } catch (error) {
        console.log(`尝试点击按钮 "${buttonName}" 失败: ${error.message}`);
        continue;
      }
    }

    throw new Error(`未找到任何可用的按钮: ${searchButtonNames.join(', ')}`);

  } catch (error) {
    console.log(`对话框操作失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      buttonNames: buttonNames
    };
  }
}

// 带网络监听的确认操作
async function performConfirmWithNetworkMonitoring(page, button, buttonName, timeout) {
  try {
    // 动态导入网络监听工具
    const { executeWithNetworkMonitoring } = await import('../../utils/network.js');

    const result = await executeWithNetworkMonitoring(
      page,
      async () => {
        await button.click();
        return { clicked: true };
      },
      {
        timeout: timeout || DIALOG_CONSTANTS.WAIT_TIMEOUT,
        expectSuccess: true,
        successIndicator: async (page) => {
          await page.waitForTimeout(timeout || DIALOG_CONSTANTS.WAIT_TIMEOUT);

          try {
            const checkButton = await page.getByRole(DIALOG_CONSTANTS.BUTTON_ROLE, { name: buttonName });
            const isVisible = await checkButton.isVisible({ timeout: 1000 });
            return !isVisible;
          } catch (error) {
            return true;
          }
        }
      }
    );

    if (result.success) {
      return {
        success: true,
        buttonName: buttonName,
        clicked: true,
        method: 'network_monitored',
        network: {
          requests: result.network.requests,
          responses: result.network.responses,
          duration: result.network.duration
        }
      };
    } else {
      throw new Error(`对话框确认失败，对话框未关闭`);
    }

  } catch (error) {
    return {
      success: false,
      buttonName: buttonName,
      error: error.message,
      method: 'network_monitored_failed'
    };
  }
}



// 脚本元数据
export const metadata = {
  name: "dialog-operations",
  description: "对话框操作工具，支持确认、取消、关闭等对话框操作",
  version: "2.0.0",
  parameters: {
    actionType: {
      type: "string",
      required: false,
      description: "操作类型: confirm, cancel, close"
    },
    actionName: {
      type: "string",
      required: false,
      description: "具体操作名称（优先级高于 actionType），如：确定、取消、关闭等"
    },
    buttonText: {
      type: "string",
      required: false,
      description: "自定义按钮文本，用于点击特定文本的按钮"
    },
    timeout: {
      type: "number",
      default: 1000,
      description: "操作后等待时间(毫秒)"
    }
  },
  returns: {
    success: {
      type: "boolean",
      description: "操作是否成功"
    },
    message: {
      type: "string",
      description: "操作结果消息"
    },
    data: {
      type: "object",
      description: "操作结果数据（可选）",
      properties: {
        buttonName: {
          type: "string",
          description: "实际点击的按钮名称"
        },
        clicked: {
          type: "boolean",
          description: "是否成功点击按钮"
        },
        method: {
          type: "string",
          description: "使用的操作方法"
        },
        network: {
          type: "object",
          description: "网络监听信息（仅确认操作返回）"
        }
      }
    }
  },
  examples: [
    {
      description: "确认对话框",
      params: {
        actionType: "confirm"
      }
    },
    {
      description: "取消对话框",
      params: {
        actionType: "cancel"
      }
    },
    {
      description: "使用具体操作名称确认",
      params: {
        actionName: "确定"
      }
    },
    {
      description: "点击自定义按钮",
      params: {
        actionName: "保存",
        buttonText: "保存并继续"
      }
    },
    {
      description: "关闭对话框",
      params: {
        actionType: "close"
      }
    }
  ],
  tags: ["dialog", "ui", "interaction", "confirm", "cancel", "close"]
};
