/**
 * 脚本注册表
 * 管理所有已注册的脚本，提供查找和验证功能
 */

import type { ScriptDefinition, ScriptMetadata } from './types';
import type { ValidationResult } from '../validation/yaml-validator';
import { ScriptValidator } from './script-validator';
import { logger } from '../utils/logger';

export class ScriptRegistry {
  private scripts = new Map<string, ScriptDefinition>();
  private validator = new ScriptValidator();

  /**
   * 注册脚本
   */
  register(script: ScriptDefinition): ValidationResult {
    try {
      // 验证元数据
      const metadataValidation = this.validator.validateMetadata(script.metadata);
      if (!metadataValidation.isValid) {
        logger.error('脚本元数据验证失败', { 
          scriptName: script.name, 
          errors: metadataValidation.errors 
        });
        return metadataValidation;
      }

      // 检查名称冲突
      if (this.scripts.has(script.name)) {
        logger.warn('脚本名称冲突，将覆盖现有脚本', { 
          scriptName: script.name,
          existingPath: this.scripts.get(script.name)?.filePath,
          newPath: script.filePath
        });
      }

      // 注册脚本
      this.scripts.set(script.name, script);
      
      logger.info('脚本注册成功', { 
        scriptName: script.name, 
        filePath: script.filePath,
        description: script.metadata.description
      });

      return {
        isValid: true,
        errors: [],
        warnings: []
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('脚本注册失败', { 
        scriptName: script.name, 
        error: errorMessage 
      });

      return {
        isValid: false,
        errors: [{
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `脚本注册失败: ${errorMessage}`,
          suggestions: ['检查脚本文件格式是否正确']
        }],
        warnings: []
      };
    }
  }

  /**
   * 获取脚本
   */
  getScript(name: string): ScriptDefinition | undefined {
    return this.scripts.get(name);
  }

  /**
   * 检查脚本是否存在
   */
  hasScript(name: string): boolean {
    return this.scripts.has(name);
  }

  /**
   * 获取所有脚本名称
   */
  getScriptNames(): string[] {
    return Array.from(this.scripts.keys());
  }

  /**
   * 获取所有脚本
   */
  getAllScripts(): ScriptDefinition[] {
    return Array.from(this.scripts.values());
  }

  /**
   * 根据标签筛选脚本
   */
  getScriptsByTag(tag: string): ScriptDefinition[] {
    return Array.from(this.scripts.values()).filter(script => 
      script.metadata.tags?.includes(tag)
    );
  }

  /**
   * 验证脚本参数
   */
  validateScriptParameters(scriptName: string, parameters: any): ValidationResult {
    const script = this.scripts.get(scriptName);
    if (!script) {
      return {
        isValid: false,
        errors: [{
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `脚本不存在: ${scriptName}`,
          suggestions: ['检查脚本名称是否正确', '确保脚本已正确加载']
        }],
        warnings: []
      };
    }

    return this.validator.validateParameters(script.metadata, parameters || {});
  }

  /**
   * 获取脚本统计信息
   */
  getStatistics() {
    const scripts = Array.from(this.scripts.values());
    const tagCounts = new Map<string, number>();

    scripts.forEach(script => {
      script.metadata.tags?.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    return {
      totalScripts: scripts.length,
      scriptNames: scripts.map(s => s.name),
      tagCounts: Object.fromEntries(tagCounts),
      scriptsWithParameters: scripts.filter(s => s.metadata.parameters && Object.keys(s.metadata.parameters).length > 0).length,
      scriptsWithReturns: scripts.filter(s => s.metadata.returns && Object.keys(s.metadata.returns).length > 0).length
    };
  }

  /**
   * 清空注册表
   */
  clear(): void {
    this.scripts.clear();
    logger.info('脚本注册表已清空');
  }

  /**
   * 移除指定脚本
   */
  unregister(scriptName: string): boolean {
    const removed = this.scripts.delete(scriptName);
    if (removed) {
      logger.info('脚本已移除', { scriptName });
    }
    return removed;
  }
}
