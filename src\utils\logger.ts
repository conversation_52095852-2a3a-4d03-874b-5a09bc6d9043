import winston from 'winston';

export interface LoggerConfig {
  level?: string;
  format?: 'json' | 'simple' | 'colorize';
  output?: {
    console?: boolean;
    file?: string;
  };
}

export class Logger {
  private logger: winston.Logger;

  constructor(config: LoggerConfig = {}) {
    const {
      level = 'info',
      format = 'colorize',
      output = { console: true }
    } = config;

    const formats = [];

    // Add timestamp
    formats.push(winston.format.timestamp());

    // Add format based on configuration
    switch (format) {
      case 'json':
        formats.push(winston.format.json());
        break;
      case 'simple':
        formats.push(winston.format.simple());
        break;
      case 'colorize':
        formats.push(
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
            return `${timestamp} [${level}]: ${message} ${metaStr}`;
          })
        );
        break;
      default:
        break;
      }

    const transports: winston.transport[] = [];

    if (output.console) {
      transports.push(new winston.transports.Console());
    }

    if (output.file) {
      transports.push(new winston.transports.File({ filename: output.file }));
    }

    this.logger = winston.createLogger({
      level,
      format: winston.format.combine(...formats),
      transports,
    });
  }

  debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  error(message: string, meta?: any): void {
    this.logger.error(message, meta);
  }

  // Create child logger with additional context
  child(defaultMeta: any): Logger {
    const childLogger = new Logger();
    childLogger.logger = this.logger.child(defaultMeta);
    return childLogger;
  }

  // Performance logging helper
  time(label: string): void {
    console.time(label);
  }

  timeEnd(label: string): void {
    console.timeEnd(label);
  }
}

// Default logger instance
export const logger = new Logger({
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT as any || 'colorize',
  output: {
    console: true,
    file: process.env.LOG_FILE,
  },
}); 