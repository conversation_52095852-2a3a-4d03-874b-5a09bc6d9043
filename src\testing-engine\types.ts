/**
 * TestingEngine 统一配置接口
 * 简化设计，只保留真正需要的配置
 */

export type ExecutorType = 'web' | 'element-plus';
export type ConnectionType = 'new-browser' | 'cdp';

/**
 * TestingEngine 配置选项
 */
export interface EngineOptions {
  // === 核心配置 ===
  /** 执行器类型 */
  executorType?: ExecutorType;
  /** 是否无头模式 */
  headless?: boolean;
  /** 慢动作延迟（毫秒） */
  slowMo?: number;
  /** 默认超时时间（毫秒） */
  timeout?: number;
  
  // === 连接配置 ===
  /** 连接类型 */
  connectionType?: ConnectionType;
  /** CDP 端点地址 */
  cdpEndpoint?: string;
  /** 调试端口 */
  debuggingPort?: number;
  /** 自动端口检测 */
  autoPort?: boolean;
  
  // === 可选配置 ===
  /** 输出目录 */
  outputDir?: string;
  /** 模板目录 */
  templateDirs?: string[];
  /** 是否截图 */
  captureScreenshots?: boolean;
  /** 失败时是否继续 */
  continueOnFailure?: boolean;
  /** 测试失败后是否强制执行清理 hooks */
  forceCleanupOnFailure?: boolean;
}

/**
 * 测试运行时选项（会覆盖引擎配置）
 */
export interface TestOptions {
  /** 超时时间 */
  timeout?: number;
  /** 是否截图 */
  captureScreenshots?: boolean;
  /** 失败时是否继续 */
  continueOnFailure?: boolean;
  /** 测试失败后是否强制执行清理 hooks */
  forceCleanupOnFailure?: boolean;
  /** 输出目录 */
  outputDir?: string;
  /** 标签过滤 */
  tags?: string[];
}

/**
 * 步骤执行选项
 */
export interface StepOptions {
  /** 执行器类型 */
  executorType?: ExecutorType;
  /** 超时时间 */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
}

/**
 * 解析后的最终配置
 */
export interface ResolvedConfig {
  // 核心配置
  executorType: ExecutorType;
  headless: boolean;
  slowMo: number;
  timeout: number;
  
  // 连接配置
  connectionType: ConnectionType;
  cdpEndpoint?: string;
  debuggingPort?: number;
  autoPort: boolean;
  
  // 测试配置
  outputDir: string;
  templateDirs: string[];
  captureScreenshots: boolean;
  continueOnFailure: boolean;
  forceCleanupOnFailure: boolean;
  
  // YAML 配置（运行时合并）
  baseUrl?: string;
  variables?: Record<string, any>;
}

/**
 * 测试结果接口
 */
export interface TestResult {
  success: boolean;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
  };
  results: any[];
  errors: string[];
}

/**
 * 步骤结果接口
 */
export interface StepResult {
  success: boolean;
  duration: number;
  error?: string;
  data?: any;
}

/**
 * 步骤数据接口（简化）
 */
export interface StepData {
  id?: string;
  name?: string;
  action: string;
  [key: string]: any;
}

/**
 * 系统状态接口
 */
export interface SystemStatus {
  initialized: boolean;
  runningTests?: any[];
  connectionType?: ConnectionType;
  executorType?: ExecutorType;
}
