/**
 * 测试executeTemplateTest方法的修改
 */

// 模拟YAML模板内容
const templateYaml = `
templates:
  search-form-template:
    name: "奶茶车管理搜索表单模板"
    type: "shared"
    parameters:
      - name: modelNumber
        type: "string"
        required: false
      - name: sellCarNameOrCode
        type: "string"
        required: false
    steps:
      # 选择型号
      - action: selectOption
        role: combobox
        roleOptions:
          name: "型号"
        data: "{{modelNumber}}"
      
      # 输入车辆名称/编码
      - action: fill
        role: textbox
        roleOptions:
          name: "车辆名称/编码"
        data: "{{sellCarNameOrCode}}"
      
      # 点击查询按钮
      - action: click
        role: button
        roleOptions:
          name: "查询"
      
      # 等待查询结果加载
      - action: wait
        data: 1000
`;

// 模拟参数
const parameters = {
  "search-form-template": {
    modelNumber: "Model-A",
    sellCarNameOrCode: "Car123"
  }
};

// 测试解析功能
function testParseYamlTemplates() {
  console.log('=== 测试YAML解析 ===');
  
  try {
    const yaml = require('yaml');
    const parsed = yaml.parse(templateYaml);
    
    console.log('解析成功:');
    console.log('- 模板数量:', Object.keys(parsed.templates || {}).length);
    console.log('- 模板名称:', Object.keys(parsed.templates || {}));
    console.log('- 第一个模板:', JSON.stringify(parsed.templates['search-form-template'], null, 2));
    
  } catch (error) {
    console.error('解析失败:', error.message);
  }
}

// 测试创建useTemplate步骤
function testCreateUseTemplateSteps() {
  console.log('\n=== 测试创建useTemplate步骤 ===');
  
  const steps = [];
  
  // 模拟createUseTemplateSteps方法的逻辑
  for (const [templateName, templateParams] of Object.entries(parameters)) {
    steps.push({
      action: 'useTemplate',
      template: templateName,
      parameters: templateParams,
      description: `执行模板: ${templateName}`
    });
  }
  
  console.log('生成的步骤:');
  console.log(JSON.stringify(steps, null, 2));
}

// 测试构建完整测试套件
function testBuildTestSuite() {
  console.log('\n=== 测试构建测试套件 ===');
  
  try {
    const yaml = require('yaml');
    const parsedYaml = yaml.parse(templateYaml);
    
    // 创建useTemplate步骤
    const useTemplateSteps = [];
    for (const [templateName, templateParams] of Object.entries(parameters)) {
      useTemplateSteps.push({
        action: 'useTemplate',
        template: templateName,
        parameters: templateParams,
        description: `执行模板: ${templateName}`
      });
    }
    
    // 构建测试套件
    const testSuite = {
      config: {
        name: "临时模板测试",
        testMode: "flow",
        executorType: "web"
      },
      templates: parsedYaml.templates || {},
      tests: [
        {
          name: "执行模板测试",
          description: "通过useTemplate动作执行指定的模板",
          steps: useTemplateSteps
        }
      ]
    };
    
    const testSuiteYaml = yaml.stringify(testSuite);
    
    console.log('生成的测试套件YAML:');
    console.log(testSuiteYaml);
    
  } catch (error) {
    console.error('构建失败:', error.message);
  }
}

// 运行所有测试
function runTests() {
  console.log('开始测试executeTemplateTest方法的修改...\n');
  
  testParseYamlTemplates();
  testCreateUseTemplateSteps();
  testBuildTestSuite();
  
  console.log('\n测试完成!');
}

// 如果直接运行此文件
if (require.main === module) {
  runTests();
}

module.exports = {
  testParseYamlTemplates,
  testCreateUseTemplateSteps,
  testBuildTestSuite,
  runTests
};
