{"$schema": "https://biomejs.dev/schemas/1.4.1/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "error", "useExportType": "error"}, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noExplicitAny": "off"}, "complexity": {"noForEach": "off", "noStaticOnlyClass": "off", "noThisInStatic": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf", "attributePosition": "auto"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}}, "json": {"formatter": {"trailingCommas": "none"}}, "files": {"include": ["packages/**/*.ts", "packages/**/*.js", "packages/**/*.json", "scripts/**/*.ts", "scripts/**/*.js", "test/**/*.ts", "*.ts", "*.js", "*.json"], "ignore": ["node_modules", "dist", "coverage", "*.min.js"]}}