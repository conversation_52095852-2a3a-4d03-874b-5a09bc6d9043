/**
 * 统一路径解析服务
 * 解决路径解析一致性问题，支持环境变量覆盖
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import * as dotenv from 'dotenv';
import { logger } from './logger';

export interface PathConfig {
  /** 项目根目录 */
  rootDir: string;
  /** 模板文件目录 */
  templatesDir: string;
  /** 脚本文件目录 */
  scriptsDir: string;
  /** 测试结果输出目录 */
  outputDir: string;
}

export interface PathResolverOptions {
  /** 自定义项目根目录 */
  rootDir?: string;
  /** 是否启用环境变量覆盖 */
  enableEnvOverride?: boolean;
  /** 是否启用向后兼容模式 */
  enableLegacyMode?: boolean;
}

/**
 * 路径解析器
 * 提供统一的路径解析服务，解决相对路径依赖执行目录的问题
 */
export class PathResolver {
  private config: PathConfig;
  private options: Required<PathResolverOptions>;

  constructor(options: PathResolverOptions = {}) {
    // 加载环境变量
    this.loadEnvironmentVariables();

    this.options = {
      rootDir: options.rootDir || this.findProjectRoot(),
      enableEnvOverride: options.enableEnvOverride ?? true,
      enableLegacyMode: options.enableLegacyMode ?? true,
    };

    this.config = this.buildPathConfig();

    logger.debug('PathResolver 初始化完成', {
      rootDir: this.config.rootDir,
      config: this.config,
      options: this.options
    });
  }

  /**
   * 获取路径配置
   */
  getPathConfig(): PathConfig {
    return { ...this.config };
  }

  /**
   * 解析绝对路径
   */
  resolve(relativePath: string): string {
    if (path.isAbsolute(relativePath)) {
      return relativePath;
    }
    return path.resolve(this.config.rootDir, relativePath);
  }

  /**
   * 获取模板目录路径
   */
  getTemplatesDir(): string {
    return this.config.templatesDir;
  }

  /**
   * 获取脚本目录路径
   */
  getScriptsDir(): string {
    return this.config.scriptsDir;
  }

  /**
   * 获取输出目录路径
   */
  getOutputDir(): string {
    return this.config.outputDir;
  }

  /**
   * 获取配置目录路径（已废弃，配置直接从根目录 .env 加载）
   * @deprecated 使用根目录的 .env 文件进行配置
   */
  getConfigDir(): string {
    // 返回根目录，因为配置文件现在直接在根目录
    return this.config.rootDir;
  }


  /**
   * 获取项目根目录
   */
  getRootDir(): string {
    return this.config.rootDir;
  }

  /**
   * 检查路径是否存在
   */
  exists(relativePath: string): boolean {
    const absolutePath = this.resolve(relativePath);
    return fs.existsSync(absolutePath);
  }

  /**
   * 确保目录存在
   */
  ensureDir(relativePath: string): string {
    const absolutePath = this.resolve(relativePath);
    if (!fs.existsSync(absolutePath)) {
      fs.mkdirSync(absolutePath, { recursive: true });
      logger.debug('创建目录', { path: absolutePath });
    }
    return absolutePath;
  }

  /**
   * 获取相对于项目根目录的相对路径
   */
  relative(absolutePath: string): string {
    return path.relative(this.config.rootDir, absolutePath);
  }

  /**
   * 构建路径配置
   */
  private buildPathConfig(): PathConfig {
    const rootDir = this.options.rootDir;

    // 默认路径配置
    const defaultConfig: PathConfig = {
      rootDir,
      templatesDir: path.resolve(rootDir, '.automation/templates'),
      scriptsDir: path.resolve(rootDir, '.automation/scripts'),
      outputDir: path.resolve(rootDir, './test-results'),
    };

    // 环境变量覆盖
    if (this.options.enableEnvOverride) {
      const envConfig = this.loadEnvPathConfig(rootDir);
      Object.assign(defaultConfig, envConfig);
    }

    // 向后兼容模式
    if (this.options.enableLegacyMode) {
      this.applyLegacyCompatibility(defaultConfig);
    }

    return defaultConfig;
  }

  /**
   * 从环境变量加载路径配置
   */
  private loadEnvPathConfig(rootDir: string): Partial<PathConfig> {
    const envConfig: Partial<PathConfig> = {};

    if (process.env.AUTOMATION_ROOT_DIR) {
      envConfig.rootDir = path.resolve(process.env.AUTOMATION_ROOT_DIR);
    }

    if (process.env.AUTOMATION_TEMPLATES_DIR) {
      envConfig.templatesDir = path.resolve(rootDir, process.env.AUTOMATION_TEMPLATES_DIR);
    }

    if (process.env.AUTOMATION_SCRIPTS_DIR) {
      envConfig.scriptsDir = path.resolve(rootDir, process.env.AUTOMATION_SCRIPTS_DIR);
    }

    if (process.env.AUTOMATION_OUTPUT_DIR) {
      envConfig.outputDir = path.resolve(rootDir, process.env.AUTOMATION_OUTPUT_DIR);
    }

    if (Object.keys(envConfig).length > 0) {
      logger.debug('从环境变量加载路径配置', envConfig);
    }

    return envConfig;
  }

  /**
   * 应用向后兼容性
   */
  private applyLegacyCompatibility(config: PathConfig): void {
    // 检查是否存在旧的 templates 目录
    const legacyTemplatesDir = path.resolve(config.rootDir, 'templates');
    if (fs.existsSync(legacyTemplatesDir) && !fs.existsSync(config.templatesDir)) {
      config.templatesDir = legacyTemplatesDir;
      logger.info('使用向后兼容的模板目录', { path: legacyTemplatesDir });
    }

    // 检查是否存在旧的 scripts 目录
    const legacyScriptsDir = path.resolve(config.rootDir, 'scripts');
    if (fs.existsSync(legacyScriptsDir) && !fs.existsSync(config.scriptsDir)) {
      config.scriptsDir = legacyScriptsDir;
      logger.info('使用向后兼容的脚本目录', { path: legacyScriptsDir });
    }
  }

  /**
   * 查找项目根目录
   */
  private findProjectRoot(): string {
    let currentDir = process.cwd();
    
    // 向上查找包含 package.json 的目录
    while (currentDir !== path.dirname(currentDir)) {
      const packageJsonPath = path.join(currentDir, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        logger.debug('找到项目根目录', { path: currentDir });
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }

    // 如果找不到 package.json，使用当前工作目录
    const fallbackDir = process.cwd();
    logger.warn('未找到 package.json，使用当前工作目录作为项目根目录', { path: fallbackDir });
    return fallbackDir;
  }

  /**
   * 加载环境变量
   */
  private loadEnvironmentVariables(): void {
    // 查找项目根目录的 .env 文件
    const rootDir = this.findProjectRoot();
    const envPath = path.join(rootDir, '.env');

    if (fs.existsSync(envPath)) {
      dotenv.config({ path: envPath });
      logger.debug('加载环境变量文件', { path: envPath });
    } else {
      logger.debug('未找到 .env 文件', { searchPath: envPath });
    }
  }

  /**
   * 创建路径解析器实例（单例模式）
   */
  private static instance: PathResolver | null = null;

  static getInstance(options?: PathResolverOptions): PathResolver {
    if (!PathResolver.instance) {
      PathResolver.instance = new PathResolver(options);
    }
    return PathResolver.instance;
  }

  /**
   * 重置单例实例（主要用于测试）
   */
  static resetInstance(): void {
    PathResolver.instance = null;
  }
}
