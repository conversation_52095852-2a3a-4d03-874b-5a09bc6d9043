/**
 * 脚本验证器
 * 使用AJV验证脚本元数据，复用现有验证基础设施
 */

import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import type { ValidationResult, ValidationError } from '../validation/yaml-validator';
import type { ScriptMetadata } from './types';
import { logger } from '../utils/logger';

export class ScriptValidator {
  private ajv: Ajv;
  private metadataValidator: any;

  constructor() {
    this.ajv = new Ajv({ 
      allErrors: true, 
      verbose: true,
      strict: false 
    });
    addFormats(this.ajv);
    
    // 定义脚本元数据的JSON Schema
    const metadataSchema = this.createMetadataSchema();
    this.metadataValidator = this.ajv.compile(metadataSchema);
  }

  /**
   * 验证脚本元数据
   */
  validateMetadata(metadata: any): ValidationResult {
    try {
      const valid = this.metadataValidator(metadata);
      
      if (!valid) {

        const errors: ValidationError[] = this.metadataValidator.errors?.map((error: any) => ({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `脚本元数据验证失败: ${error.instancePath} ${error.message}`,
          suggestions: ['检查脚本元数据格式是否正确', '参考文档中的元数据定义示例'],
        })) || [];

        return {
          isValid: false,
          errors,
          warnings: []
        };
      }

      return {
        isValid: true,
        errors: [],
        warnings: []
      };

    } catch (error) {
      logger.error('脚本元数据验证异常', { error: error instanceof Error ? error.message : String(error) });
      
      return {
        isValid: false,
        errors: [{
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `元数据验证异常: ${error instanceof Error ? error.message : String(error)}`,
          suggestions: ['检查元数据格式是否为有效的JavaScript对象']
        }],
        warnings: []
      };
    }
  }

  /**
   * 验证脚本参数
   */
  validateParameters(metadata: ScriptMetadata, params: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    if (!metadata.parameters) {
      return { isValid: true, errors: [], warnings: [] };
    }

    // 检查必需参数
    for (const [paramName, paramDef] of Object.entries(metadata.parameters)) {
      if (paramDef.required && (params[paramName] === undefined || params[paramName] === null)) {
        errors.push({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `缺少必需参数: ${paramName}`,
          suggestions: [`为脚本 ${metadata.name} 提供参数 ${paramName}`]
        });
      }
    }

    // 检查参数类型（简单验证）
    for (const [paramName, paramValue] of Object.entries(params)) {
      const paramDef = metadata.parameters[paramName];
      if (paramDef && !this.isValidType(paramValue, paramDef.type)) {
        errors.push({
          type: 'STRUCTURE',
          severity: 'ERROR',
          message: `参数 ${paramName} 类型错误，期望 ${paramDef.type}，实际 ${typeof paramValue}`,
          suggestions: [`确保参数 ${paramName} 的类型为 ${paramDef.type}`]
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 创建元数据验证Schema
   */
  private createMetadataSchema() {
    return {
      type: 'object',
      required: ['name', 'description'],
      properties: {
        name: {
          type: 'string',
          pattern: '^[a-zA-Z][a-zA-Z0-9-_]*$',
          description: '脚本名称，只能包含字母、数字、连字符和下划线'
        },
        description: {
          type: 'string',
          minLength: 1,
          description: '脚本描述'
        },
        version: {
          type: 'string',
          pattern: '^\\d+\\.\\d+\\.\\d+$',
          description: '版本号，格式为 x.y.z'
        },
        parameters: {
          type: 'object',
          patternProperties: {
            '^[a-zA-Z][a-zA-Z0-9_]*$': {
              type: 'object',
              required: ['type'],
              properties: {
                type: {
                  enum: ['string', 'number', 'boolean', 'array', 'object'],
                  description: '参数类型'
                },
                required: {
                  type: 'boolean',
                  default: false,
                  description: '是否必需'
                },
                default: {
                  description: '默认值'
                },
                description: {
                  type: 'string',
                  description: '参数描述'
                }
              },
              additionalProperties: true
            }
          },
          additionalProperties: false
        },
        returns: {
          type: 'object',
          patternProperties: {
            '^[a-zA-Z][a-zA-Z0-9_]*$': {
              type: 'object',
              required: ['type'],
              properties: {
                type: {
                  enum: ['string', 'number', 'boolean', 'array', 'object'],
                  description: '返回值类型'
                },
                description: {
                  type: 'string',
                  description: '返回值描述'
                },
                properties: {
                  type: 'object',
                  description: '对象类型的属性定义',
                  additionalProperties: true
                },
                items: {
                  type: 'object',
                  description: '数组类型的元素定义',
                  additionalProperties: true
                }
              },
              additionalProperties: true
            }
          },
          additionalProperties: false
        },
        tags: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: '标签'
        }
      },
      additionalProperties: true
    };
  }

  /**
   * 简单的类型检查
   */
  private isValidType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }
}
