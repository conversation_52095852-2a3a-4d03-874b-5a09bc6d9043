import type { <PERSON>, Browser, BrowserContext } from '@playwright/test';
import type { TestRunResult } from '../runner/test-reporter';
import type { ExecutorType, TestRunnerOptions, TestStep } from './index';

/**
 * 测试执行上下文类型
 */
export type ExecutionContextType = 'page' | 'automation' | 'scheduled' | 'batch';

/**
 * 测试任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * 测试任务优先级
 */
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 测试执行选项
 */
export interface TestExecutionOptions {
  timeout?: number;
  continueOnFailure?: boolean;
  captureScreenshots?: boolean;
  verbose?: boolean;
  priority?: TaskPriority;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * 测试任务信息
 */
export interface TestTask {
  id: string;
  yamlContent: string;
  options: TestExecutionOptions;
  contextType: ExecutionContextType;
  status: TaskStatus;
  priority: TaskPriority;
  startTime: number;
  endTime?: number;
  duration?: number;
  result?: TestRunResult;
  error?: string;
  executorId?: string;
  sourcePageId?: string;
  metadata: Record<string, any>;
  abortController: AbortController;
}

/**
 * 测试进度信息
 */
export interface TestProgress {
  taskId: string;
  status: TaskStatus;
  currentStep: number;
  totalSteps: number;
  stepName?: string;
  progress: number; // 0-100
  logs: string[];
  screenshots: string[];
  startTime: number;
  elapsedTime: number;
}

/**
 * 执行器接口
 */
export interface TestExecutor {
  readonly id: string;
  readonly type: ExecutionContextType;
  readonly isAvailable: boolean;
  
  /**
   * 执行测试
   */
  executeTest(task: TestTask): Promise<TestRunResult>;
  
  /**
   * 取消测试
   */
  cancelTest(taskId: string): Promise<boolean>;
  
  /**
   * 获取当前运行的测试
   */
  getRunningTests(): string[];
  
  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}

/**
 * 页面执行器接口
 */
export interface PageExecutor extends TestExecutor {
  readonly type: 'page';
  readonly pageId: string;
  readonly page: Page;
}

/**
 * 自动化执行器接口
 */
export interface AutomationExecutor extends TestExecutor {
  readonly type: 'automation';
  readonly browser?: Browser;
  readonly context?: BrowserContext;
  readonly page?: Page;
}

/**
 * 全局任务管理器接口
 */
export interface GlobalTaskManager {
  /**
   * 执行测试（页面调用）
   */
  executePageTest(
    yamlContent: string, 
    page: Page, 
    pageId: string, 
    options?: TestExecutionOptions
  ): Promise<string>;
  
  /**
   * 执行单步Action直接验证（轻量级）
   */
  executeStepActionTest(
    stepData: TestStep,
    options: {executorType: ExecutorType},
    pageId?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }>;
  
  /**
   * 执行模板测试
   */
  executeTemplateTest(
    templateId: string,
    parameters?: any,
    pageId?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }>;
  
  /**
   * 执行测试（自动化调用）
   */
  executeAutomationTest(
    yamlContent: string, 
    options?: TestExecutionOptions & { runnerOptions?: TestRunnerOptions }
  ): Promise<string>;
  
  /**
   * 获取测试结果
   */
  getTestResult(taskId: string): TestRunResult | null;
  
  /**
   * 获取测试进度
   */
  getTestProgress(taskId: string): TestProgress | null;
  
  /**
   * 取消测试
   */
  cancelTest(taskId: string): Promise<boolean>;
  
  /**
   * 获取运行中的测试
   */
  getRunningTests(): TestTask[];
  
  /**
   * 获取测试历史
   */
  getTestHistory(limit?: number): TestTask[];

  /**
   * 验证YAML内容
   */
  validateYaml(yamlContent: string): Promise<{
    isValid: boolean;
    errors: Array<{
      type: string;
      severity: string;
      message: string;
      suggestions: string[];
    }>;
    warnings: Array<{
      type: string;
      severity: string;
      message: string;
      suggestions: string[];
    }>;
  }>;

  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}

/**
 * 任务路由策略
 */
export interface TaskRoutingStrategy {
  /**
   * 选择执行器
   */
  selectExecutor(
    task: TestTask, 
    availableExecutors: TestExecutor[]
  ): TestExecutor | null;
}

/**
 * 状态聚合器接口
 */
export interface StatusAggregator {
  /**
   * 聚合多个执行器的状态
   */
  aggregateStatus(executors: TestExecutor[]): {
    totalTasks: number;
    runningTasks: number;
    completedTasks: number;
    failedTasks: number;
    executorStats: Record<string, any>;
  };
}
