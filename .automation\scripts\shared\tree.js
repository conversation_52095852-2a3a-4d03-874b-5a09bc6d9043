/**
 * 树形控件操作脚本
 * 提供树节点的展开、收起、点击、搜索等功能
 */

// 常量定义
const TREE_CONSTANTS = {
  // 角色定义
  TREE_ROLE: 'tree',
  TREEITEM_ROLE: 'treeitem',
  MENU_ROLE: 'menu',
  BUTTON_ROLE: 'button',
  GROUP_ROLE: 'group',

  // 等待时间
  WAIT_TIMEOUT: 1000,
  SHORT_WAIT: 500,

  // 其他
  HOVER_FORCE: true
};

// 操作类型判断函数 - 简化为英文操作名称
function isExpandOperation(operation) {
  return operation === 'expand';
}

function isCollapseOperation(operation) {
  return operation === 'collapse';
}

function isToggleOperation(operation) {
  return operation === 'toggle';
}

function isClickOperation(operation) {
  return operation === 'click';
}

function isFindOperation(operation) {
  return operation === 'find';
}

function isExistsOperation(operation) {
  return operation === 'exists';
}

function isDoubleClickOperation(operation) {
  return operation === 'doubleClick';
}

function isRightClickOperation(operation) {
  return operation === 'rightClick';
}

function isClickButtonOperation(operation) {
  return operation === 'clickButton';
}

function isGetInfoOperation(operation) {
  return operation === 'getInfo';
}

export default async function treeOperations(params, context) {
  const {
    actionType,
    itemName,
    index,
    path,
    selector,
    buttonText,
    exact = true,
    timeout = TREE_CONSTANTS.WAIT_TIMEOUT
  } = params;
  const { page, logger } = context;

  if (!actionType) {
    return {
      success: false,
      message: '必须指定 actionType 参数'
    };
  }

  logger.info('开始树操作', { actionType, itemName, index, path, selector, buttonText, exact, timeout });

  try {
    let result;

    // 根据操作类型执行相应的操作
    if (isExpandOperation(actionType)) {
      result = await expandTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isCollapseOperation(actionType)) {
      result = await collapseTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isToggleOperation(actionType)) {
      result = await toggleTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isClickOperation(actionType)) {
      result = await clickTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isDoubleClickOperation(actionType)) {
      result = await doubleClickTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isRightClickOperation(actionType)) {
      result = await rightClickTreeItem(page, { itemName, index, path, selector }, logger);
    } else if (isFindOperation(actionType)) {
      result = await findTreeItem(page, { itemName, index, path, selector, exact }, logger);
    } else if (isExistsOperation(actionType)) {
      result = await checkTreeItemExists(page, { itemName, index, path, selector }, logger);
    } else if (isClickButtonOperation(actionType)) {
      result = await clickTreeNodeButton(page, { itemName, index, path, selector }, buttonText, logger);
    } else if (isGetInfoOperation(actionType)) {
      result = await getTreeItemInfo(page, { itemName, index, path, selector }, logger);
    } else {
      const supportedActions = ['expand', 'collapse', 'toggle', 'click', 'doubleClick', 'rightClick', 'find', 'exists', 'clickButton', 'getInfo'];
      return {
        success: false,
        message: `不支持的操作类型: ${actionType}。支持的操作类型: ${supportedActions.join(', ')}`
      };
    }

    const success = result === true || (typeof result === 'object' && result.success);
    const message = success ? `${actionType} 操作成功` : `${actionType} 操作失败`;

    logger.info('树操作完成', { actionType, success });

    return {
      success,
      message,
      data: typeof result === 'object' ? result : undefined
    };

  } catch (error) {
    const errorMessage = error.message || '未知错误';
    logger.error('树操作失败', { actionType, error: errorMessage });

    return {
      success: false,
      message: `${actionType} 操作失败: ${errorMessage}`
    };
  }
}

// 通用节点定位函数 - 支持多种定位方式
async function locateTreeItem(page, locator, logger) {
  const { itemName, index, path, selector } = locator;

  try {
    let element = null;

    if (selector) {
      // 使用CSS选择器
      element = page.locator(selector);
    } else if (typeof index === 'number') {
      // 使用索引
      const treeItems = page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      element = treeItems.nth(index);
    } else if (path) {
      // 使用路径（如 "根目录/子目录/文件"）
      const pathParts = path.split('/');
      element = await locateByPath(page, pathParts, logger);
    } else if (itemName) {
      // 使用名称
      element = page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });
    } else {
      throw new Error('必须提供 itemName、index、path 或 selector 中的一个');
    }

    return element;
  } catch (error) {
    logger.error('定位树节点失败', { locator, error: error.message });
    throw error;
  }
}

// 按路径定位节点（递归展开路径）
async function locateByPath(page, pathParts, logger) {
  let currentElement = null;

  for (let i = 0; i < pathParts.length; i++) {
    const partName = pathParts[i];
    const element = page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: partName, exact: true });

    if (!(await element.isVisible())) {
      throw new Error(`路径节点不存在: ${partName}`);
    }

    // 如果不是最后一个节点，需要展开
    if (i < pathParts.length - 1) {
      const isExpanded = await element.getAttribute('aria-expanded');
      if (isExpanded !== 'true') {
        await element.click({ force: true });
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      }
    }

    currentElement = element;
  }

  return currentElement;
}

// 查找树节点 - 统一的查找接口
async function findTreeItem(page, locator, logger) {
  logger.info('查找树节点', { locator });

  try {
    const { exact = true } = locator;
    let foundItems = [];

    if (locator.itemName && !exact) {
      // 模糊查找 - 搜索包含指定文本的节点
      const treeItems = page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      const itemCount = await treeItems.count();

      for (let i = 0; i < itemCount; i++) {
        const item = treeItems.nth(i);
        const itemText = await item.textContent();

        if (itemText && itemText.includes(locator.itemName)) {
          foundItems.push({
            index: i,
            text: itemText.trim(),
            element: item
          });
        }
      }

      return {
        success: true,
        action: 'findTreeItem',
        foundCount: foundItems.length,
        items: foundItems
      };
    } else {
      // 精确查找
      const element = await locateTreeItem(page, locator, logger);

      if (await element.isVisible()) {
        const text = await element.textContent();
        return {
          success: true,
          action: 'findTreeItem',
          item: {
            text: text?.trim() || '',
            element: element
          }
        };
      } else {
        return {
          success: false,
          action: 'findTreeItem',
          error: '树节点不可见或不存在'
        };
      }
    }
  } catch (error) {
    logger.error('查找树节点失败', { locator, error: error.message });
    return {
      success: false,
      action: 'findTreeItem',
      error: error.message
    };
  }
}

// 检查树节点是否存在
async function checkTreeItemExists(page, locator, logger) {
  logger.info('检查树节点是否存在', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);
    const exists = await element.isVisible();

    return {
      success: true,
      action: 'checkTreeItemExists',
      exists: exists
    };
  } catch (error) {
    return {
      success: true,
      action: 'checkTreeItemExists',
      exists: false
    };
  }
}

// 点击树节点 - 统一的点击接口
async function clickTreeItem(page, locator, logger) {
  logger.info('点击树节点', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      await element.click({ force: true });
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      logger.info('成功点击树节点', { locator });
      return true;
    } else {
      const errorMsg = '树节点不可见或不存在';
      logger.error(errorMsg, { locator });
      throw new Error(errorMsg);
    }
  } catch (error) {
    logger.error('点击树节点失败', { locator, error: error.message });
    throw error;
  }
}

// 双击树节点
async function doubleClickTreeItem(page, locator, logger) {
  logger.info('双击树节点', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      await element.dblclick({ force: true });
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      logger.info('成功双击树节点', { locator });
      return true;
    } else {
      logger.warn('树节点不可见', { locator });
      return false;
    }
  } catch (error) {
    logger.error('双击树节点失败', { locator, error: error.message });
    return false;
  }
}

// 右键点击树节点
async function rightClickTreeItem(page, locator, logger) {
  logger.info('右键点击树节点', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      await element.click({ button: 'right', force: true });
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      logger.info('成功右键点击树节点', { locator });
      return true;
    } else {
      logger.warn('树节点不可见', { locator });
      return false;
    }
  } catch (error) {
    logger.error('右键点击树节点失败', { locator, error: error.message });
    return false;
  }
}

// 点击树节点内的按钮
async function clickTreeNodeButton(page, locator, buttonText = null, logger) {
  logger.info('点击树节点按钮', { locator, buttonText });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (!(await element.isVisible())) {
      logger.warn('树节点不可见', { locator });
      return false;
    }

    // 在树节点内查找按钮
    const button = element.getByRole(TREE_CONSTANTS.BUTTON_ROLE);

    if (await button.isVisible()) {
      await button.hover({ force: TREE_CONSTANTS.HOVER_FORCE });
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);

      // 查找目标按钮
      let targetButton;
      if (buttonText) {
        // 如果指定了按钮文本，按文本查找
        targetButton = page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE, { name: buttonText });
      } else {
        // 否则查找第一个按钮
        targetButton = page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE).first();
      }

      if (await targetButton.isVisible()) {
        await targetButton.click({ force: true });
        logger.info('成功点击树节点按钮', { locator, buttonText });
        return true;
      } else {
        const errorMsg = `目标按钮不可见: ${buttonText || '默认按钮'}`;
        logger.error(errorMsg, { locator, buttonText });
        throw new Error(errorMsg);
      }
    } else {
      const errorMsg = '树节点中没有按钮';
      logger.error(errorMsg, { locator });
      throw new Error(errorMsg);
    }
  } catch (error) {
    logger.error('点击树节点按钮失败', { locator, buttonText, error: error.message });
    throw error;
  }
}

// 展开树节点
async function expandTreeItem(page, locator, logger) {
  logger.info('展开树节点', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      const isExpanded = await element.getAttribute('aria-expanded');

      if (isExpanded !== 'true') {
        await element.click({ force: true });
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        logger.info('成功展开树节点', { locator });
        return true;
      } else {
        logger.info('树节点已经展开', { locator });
        return true;
      }
    } else {
      const errorMsg = '树节点不可见或不存在';
      logger.error(errorMsg, { locator });
      throw new Error(errorMsg);
    }
  } catch (error) {
    logger.error('展开树节点失败', { locator, error: error.message });
    throw error;
  }
}

// 收起树节点
async function collapseTreeItem(page, locator, logger) {
  logger.info('收起树节点', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      const isExpanded = await element.getAttribute('aria-expanded');

      if (isExpanded === 'true') {
        await element.click({ force: true });
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        logger.info('成功收起树节点', { locator });
        return true;
      } else {
        logger.info('树节点已经收起', { locator });
        return true;
      }
    } else {
      const errorMsg = '树节点不可见或不存在';
      logger.error(errorMsg, { locator });
      throw new Error(errorMsg);
    }
  } catch (error) {
    logger.error('收起树节点失败', { locator, error: error.message });
    throw error;
  }
}

// 切换树节点展开/收起状态
async function toggleTreeItem(page, locator, logger) {
  logger.info('切换树节点状态', { locator });

  try {
    const element = await locateTreeItem(page, locator, logger);

    if (await element.isVisible()) {
      const isExpanded = await element.getAttribute('aria-expanded');

      await element.click({ force: true });
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);

      const newState = isExpanded === 'true' ? '收起' : '展开';
      logger.info(`成功${newState}树节点`, { locator });
      return true;
    } else {
      const errorMsg = '树节点不可见或不存在';
      logger.error(errorMsg, { locator });
      throw new Error(errorMsg);
    }
  } catch (error) {
    logger.error('切换树节点状态失败', { locator, error: error.message });
    throw error;
  }
}

// 获取树节点信息
async function getTreeItemInfo(page, locator, logger) {
  logger.info('获取树节点信息', { locator });

  try {
    if (locator && Object.keys(locator).length > 0) {
      // 获取单个节点信息
      const element = await locateTreeItem(page, locator, logger);

      if (await element.isVisible()) {
        const text = await element.textContent();
        const isExpanded = await element.getAttribute('aria-expanded');

        return {
          success: true,
          action: 'getTreeItemInfo',
          item: {
            text: text?.trim() || '',
            isExpanded: isExpanded === 'true',
            element: element
          }
        };
      } else {
        return {
          success: false,
          action: 'getTreeItemInfo',
          error: '树节点不可见或不存在'
        };
      }
    } else {
      // 获取所有节点信息
      const treeItems = page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      const itemCount = await treeItems.count();

      const allItems = [];

      for (let i = 0; i < itemCount; i++) {
        const item = treeItems.nth(i);
        const itemText = await item.textContent();
        const isExpanded = await item.getAttribute('aria-expanded');

        allItems.push({
          index: i,
          text: itemText ? itemText.trim() : '',
          isExpanded: isExpanded === 'true',
          element: item
        });
      }

      logger.info('获取树节点信息完成', { itemCount: allItems.length });
      return {
        success: true,
        action: 'getTreeItemInfo',
        itemCount: allItems.length,
        items: allItems
      };
    }
  } catch (error) {
    logger.error('获取树节点信息失败', { locator, error: error.message });
    return {
      success: false,
      action: 'getTreeItemInfo',
      error: error.message
    };
  }
}

// 脚本元数据
export const metadata = {
  name: "tree-operations",
  description: "树形控件操作工具，支持多种定位方式和语义化操作",
  version: "3.0.0",
  parameters: {
    actionType: {
      type: "string",
      required: true,
      description: "操作类型: expand, collapse, toggle, click, find, exists, clickButton"
    },
    itemName: {
      type: "string",
      required: false,
      description: "树节点名称，用于按名称定位树节点"
    },
    index: {
      type: "number",
      required: false,
      description: "树节点索引，用于按索引定位树节点"
    },
    path: {
      type: "string",
      required: false,
      description: "树节点路径，用于按路径定位树节点（如：'根目录/子目录/文件'）"
    },
    selector: {
      type: "string",
      required: false,
      description: "CSS选择器，用于按选择器定位树节点"
    },
    exact: {
      type: "boolean",
      default: true,
      description: "是否精确匹配（仅用于find操作），true=精确匹配，false=包含匹配"
    },
    buttonText: {
      type: "string",
      required: false,
      description: "按钮文本，用于点击树节点内的特定按钮"
    },
    timeout: {
      type: "number",
      default: 1000,
      description: "操作超时时间(毫秒)"
    }
  },
  returns: {
    success: {
      type: "boolean",
      description: "操作是否成功"
    },
    message: {
      type: "string",
      description: "操作结果消息"
    },
    data: {
      type: "object",
      description: "操作结果数据（可选）",
      properties: {
        foundCount: {
          type: "number",
          description: "找到的节点数（仅search返回）"
        },
        items: {
          type: "array",
          description: "找到的节点数据（仅search和getAllTreeItems返回）"
        },
        item: {
          type: "object",
          description: "找到的单个节点数据（仅find返回）"
        },
        itemCount: {
          type: "number",
          description: "树节点总数（仅getAllTreeItems返回）"
        }
      }
    }
  },
  examples: [
    {
      description: "展开名为'文件夹1'的树节点",
      params: {
        actionType: "expand",
        itemName: "文件夹1"
      }
    },
    {
      description: "收起第2个树节点",
      params: {
        actionType: "collapse",
        index: 1
      }
    },
    {
      description: "切换节点展开/收起状态",
      params: {
        actionType: "toggle",
        itemName: "根目录"
      }
    },
    {
      description: "按路径点击树节点",
      params: {
        actionType: "click",
        path: "根目录/子目录/文件.txt"
      }
    },
    // {
    //   description: "双击树节点",
    //   params: {
    //     actionType: "doubleClick",
    //     itemName: "文件1.txt"
    //   }
    // },
    // {
    //   description: "右键点击树节点",
    //   params: {
    //     actionType: "rightClick",
    //     selector: ".tree-item[data-id='123']"
    //   }
    // },
    {
      description: "精确查找树节点",
      params: {
        actionType: "find",
        itemName: "config.json",
        exact: true
      }
    },
    {
      description: "模糊搜索包含'test'的树节点",
      params: {
        actionType: "find",
        itemName: "test",
        exact: false
      }
    },
    {
      description: "检查树节点是否存在",
      params: {
        actionType: "exists",
        itemName: "不存在的文件"
      }
    },
    {
      description: "点击树节点内的按钮",
      params: {
        actionType: "clickButton",
        itemName: "文件夹1",
        buttonText: "删除"
      }
    },
    // {
    //   description: "获取单个节点信息",
    //   params: {
    //     actionType: "getInfo",
    //     itemName: "文件夹1"
    //   }
    // },
    // {
    //   description: "获取所有树节点信息",
    //   params: {
    //     actionType: "getInfo"
    //   }
    // }
  ],
  tags: ["tree", "ui", "interaction", "expand", "collapse", "toggle", "click", "doubleClick", "rightClick", "find", "exists", "button", "locator"]
};