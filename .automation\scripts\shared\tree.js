/**
 * 树形控件操作脚本
 * 提供树节点的展开、收起、点击、搜索等功能
 */

// 常量定义
const TREE_CONSTANTS = {
  // 角色定义
  TREE_ROLE: 'tree',
  TREEITEM_ROLE: 'treeitem',
  MENU_ROLE: 'menu',
  BUTTON_ROLE: 'button',
  GROUP_ROLE: 'group',

  // 等待时间
  WAIT_TIMEOUT: 1000,
  SHORT_WAIT: 500,

  // 其他
  HOVER_FORCE: true
};

// 操作类型判断函数
function isExpandOperation(operation) {
  const expandOperations = ['expand', 'expandTreeItem', '展开'];
  return expandOperations.includes(operation);
}

function isCollapseOperation(operation) {
  const collapseOperations = ['collapse', 'collapseTreeItem', '收起', '折叠'];
  return collapseOperations.includes(operation);
}

function isClickOperation(operation) {
  const clickOperations = ['click', 'clickTreeItem', 'treeItemClick', '点击'];
  return clickOperations.includes(operation);
}

function isSearchOperation(operation) {
  const searchOperations = ['search', 'searchTreeItem', '搜索'];
  return searchOperations.includes(operation);
}

function isFindOperation(operation) {
  const findOperations = ['find', 'findTreeItemByName', '查找'];
  return findOperations.includes(operation);
}

function isButtonClickOperation(operation) {
  const buttonClickOperations = ['clickButton', 'treeItemButtonClick', 'clickTreeNodeButton', '点击按钮'];
  return buttonClickOperations.includes(operation);
}

export default async function treeOperations(params, context) {
  const { actionType, actionName, itemName, index, buttonText, timeout = TREE_CONSTANTS.WAIT_TIMEOUT } = params;
  const { page, logger } = context;

  // 确定实际的操作类型（actionName 优先级更高）
  const operation = actionName || actionType;
  if (!operation) {
    return {
      success: false,
      message: '必须指定 actionType 或 actionName 参数'
    };
  }

  logger.info('开始树操作', { operation, itemName, index, buttonText, timeout });

  try {
    let result;

    // 根据操作类型执行相应的操作
    if (isExpandOperation(operation)) {
      result = await expandTreeItem(page, itemName, logger);
    } else if (isCollapseOperation(operation)) {
      result = await collapseTreeItem(page, itemName, logger);
    } else if (isClickOperation(operation)) {
      if (typeof index === 'number') {
        result = await clickTreeItemByIndex(page, index, logger);
      } else {
        result = await clickTreeItem(page, itemName, logger);
      }
    } else if (isSearchOperation(operation)) {
      result = await searchTreeItem(page, itemName, logger);
    } else if (isFindOperation(operation)) {
      result = await findTreeItemByName(page, itemName, logger);
    } else if (isButtonClickOperation(operation)) {
      result = await clickTreeNodeButton(page, itemName, buttonText, logger);
    } else if (operation === 'getAllTreeItems') {
      result = await getAllTreeItems(page, logger);
    } else {
      return {
        success: false,
        message: `不支持的操作类型: ${operation}`
      };
    }

    const success = result === true || (typeof result === 'object' && result.success);
    const message = success ? `${operation} 操作成功` : `${operation} 操作失败`;

    logger.info('树操作完成', { operation, success });

    return {
      success,
      message,
      data: typeof result === 'object' ? result : undefined
    };

  } catch (error) {
    const errorMessage = error.message || '未知错误';
    logger.error('树操作失败', { operation, error: errorMessage });

    return {
      success: false,
      message: `${operation} 操作失败: ${errorMessage}`
    };
  }
}

// 搜索指定名称的树节点
async function searchTreeItem(page, itemName, logger) {
  logger.info('搜索树节点', { itemName });

  try {
    // 获取所有树节点
    const treeItems = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
    const itemCount = await treeItems.count();

    const foundItems = [];

    for (let i = 0; i < itemCount; i++) {
      const item = treeItems.nth(i);
      const itemText = await item.textContent();

      // 检查是否包含搜索的名称
      if (itemText && itemText.includes(itemName)) {
        foundItems.push({
          index: i,
          text: itemText.trim(),
          element: item
        });
      }
    }

    logger.info('搜索完成', { foundCount: foundItems.length });
    return {
      success: true,
      action: 'searchTreeItem',
      foundCount: foundItems.length,
      items: foundItems
    };
  } catch (error) {
    logger.error('搜索树节点失败', { error: error.message });
    return {
      success: false,
      action: 'searchTreeItem',
      error: error.message
    };
  }
}

// 根据名称精确匹配树节点
async function findTreeItemByName(page, itemName, logger) {
  logger.info('查找树节点', { itemName });

  try {
    const treeItem = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });

    if (await treeItem.isVisible()) {
      return {
        success: true,
        action: 'findTreeItemByName',
        item: {
          text: itemName,
          element: treeItem
        }
      };
    }

    return {
      success: false,
      action: 'findTreeItemByName',
      error: '树节点不可见或不存在'
    };
  } catch (error) {
    logger.error('查找树节点失败', { error: error.message });
    return {
      success: false,
      action: 'findTreeItemByName',
      error: error.message
    };
  }
}

// 点击指定的树节点
async function clickTreeItem(page, itemName, logger) {
  logger.info('点击树节点', { itemName });

  try {
    const treeItem = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });

    if (await treeItem.isVisible()) {
      await treeItem.click({force: true});
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      logger.info('成功点击树节点', { itemName });
      return true;
    } else {
      logger.warn('树节点不可见', { itemName });
      return false;
    }
  } catch (error) {
    logger.error('点击树节点失败', { itemName, error: error.message });
    return false;
  }
}

// 根据索引点击树节点
async function clickTreeItemByIndex(page, index, logger) {
  logger.info('点击树节点', { index });

  try {
    const treeItems = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
    const item = treeItems.nth(index);

    if (await item.isVisible()) {
      await item.click({force: true});
      await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
      logger.info('成功点击树节点', { index });
      return true;
    } else {
      logger.warn('树节点不可见', { index });
      return false;
    }
  } catch (error) {
    logger.error('点击树节点失败', { index, error: error.message });
    return false;
  }
}

// hover 树节点中的按钮 (内部辅助函数)
async function hoverTreeButton(page, itemName, logger) {
  logger.info('hover 树节点按钮', { itemName });

  try {
    const treeItem = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });

    if (await treeItem.isVisible()) {
      // 在树节点内查找按钮
      const button = treeItem.getByRole(TREE_CONSTANTS.BUTTON_ROLE);

      if (await button.isVisible()) {
        await button.hover({ force: TREE_CONSTANTS.HOVER_FORCE });
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        logger.info('成功 hover 树节点按钮', { itemName });
        return treeItem;
      } else {
        logger.warn('树节点中的按钮不可见', { itemName });
        return null;
      }
    } else {
      logger.warn('树节点不可见', { itemName });
      return null;
    }
  } catch (error) {
    logger.error('hover 树节点按钮失败', { itemName, error: error.message });
    return null;
  }
}

// 点击树节点内的按钮
async function clickTreeNodeButton(page, itemName, buttonText = null, logger) {
  logger.info('点击树节点按钮', { itemName, buttonText });

  const treeItem = await hoverTreeButton(page, itemName, logger);
  if (!treeItem) {
    return false;
  }

  try {
    let button;
    if (buttonText) {
      // 如果指定了按钮文本，按文本查找
      button = page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE, { name: buttonText });
    } else {
      // 否则查找第一个按钮
      button = page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE).first();
    }

    if (await button.isVisible()) {
      await button.click({ force: true });
      logger.info('成功点击树节点按钮', { itemName, buttonText });
      return true;
    } else {
      logger.warn('按钮不可见', { itemName, buttonText });
      return false;
    }
  } catch (error) {
    logger.error('点击树节点按钮失败', { itemName, buttonText, error: error.message });
    return false;
  }
}

// 展开树节点
async function expandTreeItem(page, itemName, logger) {
  logger.info('展开树节点', { itemName });

  try {
    const treeItem = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName });

    if (await treeItem.isVisible()) {
      const isExpanded = await treeItem.getAttribute('aria-expanded');

      if (isExpanded !== 'true') {
        await treeItem.click({force: true});
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        logger.info('成功展开树节点', { itemName });
        return true;
      } else {
        logger.info('树节点已经展开', { itemName });
        return true;
      }
    } else {
      logger.warn('树节点不可见', { itemName });
      return false;
    }
  } catch (error) {
    logger.error('展开树节点失败', { itemName, error: error.message });
    return false;
  }
}

// 收起树节点
async function collapseTreeItem(page, itemName, logger) {
  logger.info('收起树节点', { itemName });

  try {
    const treeItem = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName });

    if (await treeItem.isVisible()) {
      const isExpanded = await treeItem.getAttribute('aria-expanded');

      if (isExpanded === 'true') {
        await treeItem.click({force: true});
        await page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        logger.info('成功收起树节点', { itemName });
        return true;
      } else {
        logger.info('树节点已经收起', { itemName });
        return true;
      }
    } else {
      logger.warn('树节点不可见', { itemName });
      return false;
    }
  } catch (error) {
    logger.error('收起树节点失败', { itemName, error: error.message });
    return false;
  }
}

// 获取所有树节点的信息
async function getAllTreeItems(page, logger) {
  logger.info('获取所有树节点信息');

  try {
    const treeItems = await page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
    const itemCount = await treeItems.count();

    const allItems = [];

    for (let i = 0; i < itemCount; i++) {
      const item = treeItems.nth(i);
      const itemText = await item.textContent();
      const isExpanded = await item.getAttribute('aria-expanded');

      allItems.push({
        index: i,
        text: itemText ? itemText.trim() : '',
        isExpanded: isExpanded === 'true',
        element: item
      });
    }

    logger.info('获取树节点信息完成', { itemCount: allItems.length });
    return {
      success: true,
      action: 'getAllTreeItems',
      itemCount: allItems.length,
      items: allItems
    };
  } catch (error) {
    logger.error('获取树节点信息失败', { error: error.message });
    return {
      success: false,
      action: 'getAllTreeItems',
      error: error.message
    };
  }
}

// 脚本元数据
export const metadata = {
  name: "tree-operations",
  description: "树形控件操作工具，支持展开、收起、点击、搜索等树节点操作",
  version: "2.0.0",
  parameters: {
    actionType: {
      type: "string",
      required: false,
      description: "操作类型: expand, collapse, click, search, find, clickButton, getAllTreeItems"
    },
    actionName: {
      type: "string",
      required: false,
      description: "具体操作名称（优先级高于 actionType），如：展开、收起、点击、搜索、查找、点击按钮等"
    },
    itemName: {
      type: "string",
      required: false,
      description: "树节点名称，用于按名称查找和操作树节点"
    },
    index: {
      type: "number",
      required: false,
      description: "树节点索引，用于按索引点击树节点"
    },
    buttonText: {
      type: "string",
      required: false,
      description: "按钮文本，用于点击树节点内的特定按钮"
    },
    timeout: {
      type: "number",
      default: 1000,
      description: "操作超时时间(毫秒)"
    }
  },
  returns: {
    success: {
      type: "boolean",
      description: "操作是否成功"
    },
    message: {
      type: "string",
      description: "操作结果消息"
    },
    data: {
      type: "object",
      description: "操作结果数据（可选）",
      properties: {
        foundCount: {
          type: "number",
          description: "找到的节点数（仅search返回）"
        },
        items: {
          type: "array",
          description: "找到的节点数据（仅search和getAllTreeItems返回）"
        },
        item: {
          type: "object",
          description: "找到的单个节点数据（仅find返回）"
        },
        itemCount: {
          type: "number",
          description: "树节点总数（仅getAllTreeItems返回）"
        }
      }
    }
  },
  examples: [
    {
      description: "展开名为'文件夹1'的树节点",
      params: {
        actionType: "expand",
        itemName: "文件夹1"
      }
    },
    {
      description: "收起名为'文件夹1'的树节点",
      params: {
        actionType: "collapse",
        itemName: "文件夹1"
      }
    },
    {
      description: "点击名为'文件1.txt'的树节点",
      params: {
        actionType: "click",
        itemName: "文件1.txt"
      }
    },
    {
      description: "点击第3个树节点（索引从0开始）",
      params: {
        actionType: "click",
        index: 2
      }
    },
    {
      description: "搜索包含'test'的树节点",
      params: {
        actionType: "search",
        itemName: "test"
      }
    },
    {
      description: "查找名为'config.json'的树节点",
      params: {
        actionType: "find",
        itemName: "config.json"
      }
    },
    {
      description: "点击'文件夹1'节点内的'删除'按钮",
      params: {
        actionType: "clickButton",
        itemName: "文件夹1",
        buttonText: "删除"
      }
    },
    {
      description: "获取所有树节点信息",
      params: {
        actionType: "getAllTreeItems"
      }
    },
    {
      description: "使用具体操作名称展开节点",
      params: {
        actionName: "展开",
        itemName: "根目录"
      }
    }
  ],
  tags: ["tree", "ui", "interaction", "expand", "collapse", "click", "search"]
};