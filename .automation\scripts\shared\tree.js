// Tree 操作相关的常量
const TREE_CONSTANTS = {
  // 角色定义
  TREE_ROLE: 'tree',
  TREEITEM_ROLE: 'treeitem',
  MENU_ROLE: 'menu',
  BUTTON_ROLE: 'button',
  GROUP_ROLE: 'group',
  
  // 等待时间
  WAIT_TIMEOUT: 1000,
  SHORT_WAIT: 500,
  
  // 其他
  HOVER_FORCE: true
};

class TreeOperations {
  constructor(page) {
    this.page = page;
  }

  // 搜索指定名称的树节点
  async searchTreeItem(itemName) {
    console.log(`搜索树节点: ${itemName}`);
    
    try {
      // 获取所有树节点
      const treeItems = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      const itemCount = await treeItems.count();
      
      const foundItems = [];
      
      for (let i = 0; i < itemCount; i++) {
        const item = treeItems.nth(i);
        const itemText = await item.textContent();
        
        // 检查是否包含搜索的名称
        if (itemText && itemText.includes(itemName)) {
          foundItems.push({
            index: i,
            text: itemText.trim(),
            element: item
          });
        }
      }
      
      console.log(`找到 ${foundItems.length} 个匹配的树节点`);
      return foundItems;
    } catch (error) {
      console.error('搜索树节点时发生错误:', error);
      return [];
    }
  }

  // 根据名称精确匹配树节点
  async findTreeItemByName(itemName) {
    console.log(`查找树节点: ${itemName}`);
    
    try {
      const treeItem = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });
      
      if (await treeItem.isVisible()) {
        return {
          text: itemName,
          element: treeItem
        };
      }
      
      return null;
    } catch (error) {
      console.error('查找树节点时发生错误:', error);
      return null;
    }
  }

  // 点击指定的树节点
  async clickTreeItem(itemName) {
    console.log(`点击树节点: ${itemName}`);
    
    try {
      const treeItem = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });
      
      if (await treeItem.isVisible()) {
        await treeItem.click({force: true});
        await this.page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        console.log(`成功点击树节点: ${itemName}`);
        return true;
      } else {
        console.log(`树节点不可见: ${itemName}`);
        return false;
      }
    } catch (error) {
      console.error('点击树节点时发生错误:', error);
      return false;
    }
  }

  // 根据索引点击树节点
  async clickTreeItemByIndex(index) {
    console.log(`点击第 ${index} 个树节点`);
    
    try {
      const treeItems = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      const item = treeItems.nth(index);
      
      if (await item.isVisible()) {
        await item.click({force: true}  );
        await this.page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
        console.log(`成功点击第 ${index} 个树节点`);
        return true;
      } else {
        console.log(`第 ${index} 个树节点不可见`);
        return false;
      }
    } catch (error) {
      console.error('点击树节点时发生错误:', error);
      return false;
    }
  }

  // hover 树节点中的按钮 (保留用于特殊场景)
  async hoverTreeButton(itemName) {
    console.log(`hover 树节点 "${itemName}" 中的按钮`);

    try {
      const treeItem = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName, exact: true });

      if (await treeItem.isVisible()) {
        // 在树节点内查找按钮
        const button = treeItem.getByRole(TREE_CONSTANTS.BUTTON_ROLE);

        if (await button.isVisible()) {
          await button.hover({ force: TREE_CONSTANTS.HOVER_FORCE });
          await this.page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
          console.log(`成功 hover 树节点 "${itemName}" 中的按钮`);
          return treeItem
        } else {
          console.log(`树节点 "${itemName}" 中的按钮不可见`);
          return null
        }
      } else {
        console.log(`树节点不可见: ${itemName}`);
        return null;
      }
    } catch (error) {
      console.error('hover 树节点按钮时发生错误:', error);
      return null;
    }
  }

  // 点击树节点内的按钮 (推荐使用，集成了hover和点击)
  async clickTreeNodeButton(itemName, buttonText = null) {
    console.log(`点击树节点 "${itemName}" 中的按钮${buttonText ? ` "${buttonText}"` : ''}`);
    const treeItem = await this.hoverTreeButton(itemName);
    if (!treeItem) {
      return false;
    }
    


      let button;
        if (buttonText) {
          // 如果指定了按钮文本，按文本查找
          button = this.page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE, { name: buttonText });
        } else {
          // 否则查找第一个按钮
          button = this.page.getByRole(TREE_CONSTANTS.MENU_ROLE).getByRole(TREE_CONSTANTS.BUTTON_ROLE).first();
    }
    if(await button.isVisible()) {
          // 再点击按钮
    await button.click({ force: true });
      return true;
    } else {
      return false;
    }
  
  }

  // 展开树节点
  async expandTreeItem(itemName) {
    console.log(`展开树节点: ${itemName}`);
    
    try {
      const treeItem = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName });
      
      if (await treeItem.isVisible()) {
        const isExpanded = await treeItem.getAttribute('aria-expanded');
        
        if (isExpanded !== 'true') {
          await treeItem.click({force: true});
          await this.page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
          console.log(`成功展开树节点: ${itemName}`);
          return true;
        } else {
          console.log(`树节点已经展开: ${itemName}`);
          return true;
        }
      } else {
        console.log(`树节点不可见: ${itemName}`);
        return false;
      }
    } catch (error) {
      console.error('展开树节点时发生错误:', error);
      return false;
    }
  }

  // 收起树节点
  async collapseTreeItem(itemName) {
    console.log(`收起树节点: ${itemName}`);
    
    try {
      const treeItem = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE, { name: itemName });
      
      if (await treeItem.isVisible()) {
        const isExpanded = await treeItem.getAttribute('aria-expanded');
        
        if (isExpanded === 'true') {
          await treeItem.click({force: true});
          await this.page.waitForTimeout(TREE_CONSTANTS.SHORT_WAIT);
          console.log(`成功收起树节点: ${itemName}`);
          return true;
        } else {
          console.log(`树节点已经收起: ${itemName}`);
          return true;
        }
      } else {
        console.log(`树节点不可见: ${itemName}`);
        return false;
      }
    } catch (error) {
      console.error('收起树节点时发生错误:', error);
      return false;
    }
  }

  // 获取所有树节点的信息
   async getAllTreeItems() {
    console.log('获取所有树节点信息');
    
    try {
      const treeItems = await this.page.getByRole(TREE_CONSTANTS.TREEITEM_ROLE);
      const itemCount = await treeItems.count();
      
      const allItems = [];
      
      for (let i = 0; i < itemCount; i++) {
        const item = treeItems.nth(i);
        const itemText = await item.textContent();
        const isExpanded = await item.getAttribute('aria-expanded');
        
        allItems.push({
          index: i,
          text: itemText ? itemText.trim() : '',
          isExpanded: isExpanded === 'true',
          element: item
        });
      }
      
      console.log(`获取到 ${allItems.length} 个树节点`);
      return allItems;
    } catch (error) {
      console.error('获取树节点信息时发生错误:', error);
      return [];
    }
  }
}

module.exports = TreeOperations;