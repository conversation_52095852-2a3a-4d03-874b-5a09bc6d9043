# Automation Testing Framework

A comprehensive, intelligent automation testing framework built on Playwright with YAML DSL, AST injection, and LLM-powered capabilities.

## 🚀 Features

### Core Capabilities
- **YAML DSL**: Write tests in human-readable YAML format
- **AST Injection**: Automatic test-id injection during build time
- **Smart Data Collection**: Runtime data correlation between UI and API
- **Template System**: Reusable test templates and atomic operations
- **LLM Integration**: AI-powered test generation and failure analysis
- **Multi-Framework Support**: Works with React, Vue, and vanilla JavaScript
- **CDP Connection**: Connect to existing browsers and reuse instances

### Key Benefits
- **Developer Friendly**: Simple YAML syntax for complex test scenarios
- **Maintainable**: Automatic test-id generation reduces brittleness
- **Intelligent**: AI assistance for test creation and debugging
- **Scalable**: Template-based approach for enterprise projects
- **Framework Agnostic**: Works with any web application

## 📦 Installation

### Using npm/yarn/pnpm
```bash
# Install globally
npm install -g @automation-testing/cli

# Or use npx
npx @automation-testing/cli init my-project
```

### Using the CLI
```bash
# Initialize a new project
auto-test init --template react-admin --name my-app

# Run tests
auto-test run --config ./test-config.yml

# Generate new test files
auto-test generate test --name user-management
```

## 🏗️ Project Structure

```
automation-testing-framework/
├── packages/
│   ├── core/                  # Core framework logic
│   ├── vite-plugin/          # Vite plugin for AST injection
│   └── cli/                  # Command line interface
├── templates/                # Test templates library
│   ├── atoms/               # Atomic operations
│   ├── crud/                # CRUD templates
│   └── modules/             # Business module templates
├── configs/                 # Configuration files
└── examples/               # Example projects
```

## 🎯 Quick Start

### 1. Initialize a Project
```bash
auto-test init --template react-admin --name my-testing-project
cd my-testing-project
```

### 2. Configure Your Tests
Create a `test-config.yml` file:

```yaml
config:
  name: "My App Tests"
  baseUrl: "http://localhost:3000"
  timeout: 30000
  
  variables:
    admin_email: "<EMAIL>"
    admin_password: "password123"

tests:
  login-test:
    name: "User Login"
    steps:
      - navigate to "{{ baseUrl }}/login"
      - fill '[data-testid="email"]' with "{{ admin_email }}"
      - fill '[data-testid="password"]' with "{{ admin_password }}"
      - click '[data-testid="login-button"]'
      - verify '[data-testid="dashboard"]' is visible
```

### 3. Set Up Vite Plugin (for automatic test-id injection)
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { automationTesting } from '@automation-testing/vite-plugin';

export default defineConfig({
  plugins: [
    automationTesting({
      enabled: true,
      testIdPrefix: 'at',
      include: ['**/*.{vue,jsx,tsx}'],
    }),
  ],
});
```

### 4. Run Tests
```bash
# Run all tests
auto-test run

# Run specific test pattern
auto-test run --test login-*

# Run with specific environment
auto-test run --env development --headless false

# Run with CDP connection (reuse browser)
auto-test run --cdp-port 9222 --keep-browser

# Connect to existing browser
auto-test run --cdp-endpoint http://localhost:9222
```

## 📝 YAML DSL Syntax

### Basic Test Structure
```yaml
config:
  name: "Test Suite Name"
  baseUrl: "http://localhost:3000"
  variables:
    user_email: "<EMAIL>"

  # CDP 连接配置
  browser:
    enableCDP: true
    debuggingPort: 9222
    autoPort: false
    headless: false

tests:
  test-name:
    name: "Human Readable Test Name"
    description: "What this test does"
    steps:
      - action: "navigate"
        data: "{{ baseUrl }}/page"
      - action: "click"
        selector: '[data-testid="button"]'
      - type: "assertion"
        action: "verify"
        selector: '[data-testid="result"]'
        expected: "Success"
```

### Shorthand Syntax
```yaml
tests:
  quick-test:
    name: "Quick Test"
    steps:
      - navigate to "{{ baseUrl }}/login"
      - fill '[data-testid="email"]' with "{{ user_email }}"
      - click '[data-testid="submit"]'
      - verify '[data-testid="message"]' contains "Welcome"
```

### Using Templates
```yaml
tests:
  user-crud:
    name: "User CRUD Operations"
    steps:
      - template: "login-template"
        parameters:
          username: "{{ admin_email }}"
          password: "{{ admin_password }}"
      - template: "crud-template"
        parameters:
          entity_name: "user"
          test_data:
            name: "John Doe"
            email: "<EMAIL>"
```

### CDP Connection Configuration
```yaml
config:
  name: "CDP Test Suite"
  browser:
    # 启用 CDP 连接模式
    enableCDP: true

    # 方式 1: 指定调试端口
    debuggingPort: 9222
    autoPort: false

    # 方式 2: 自动分配端口
    # autoPort: true

    # 方式 3: 连接到现有浏览器
    # cdpEndpoint: "http://localhost:9222"

    # 其他选项
    headless: false
    connectionTimeout: 5000

tests:
  debug-test:
    name: "调试测试"
    description: "使用 CDP 连接进行调试"
    steps:
      - navigate to "{{ baseUrl }}/debug"
      - wait for 2000  # 浏览器保持运行，便于调试
```

## 🔧 Advanced Features

### AST Injection
The Vite plugin automatically injects test IDs into your components:

```jsx
// Before (your component)
<button onClick={handleClick}>Submit</button>

// After (with AST injection)
<button onClick={handleClick} data-testid="at-submit-button">Submit</button>
```

### Data Collection & Correlation
The framework automatically correlates UI interactions with API calls:

```yaml
tests:
  api-correlation:
    name: "Test with API Correlation"
    steps:
      - click '[data-testid="load-data"]'
      - wait for api "/api/users"
      - verify response contains "<EMAIL>"
      - verify '[data-testid="user-list"]' contains "John Doe"
```

### LLM Integration
Generate tests using AI:

```bash
# Generate test from natural language
auto-test generate test --description "Test user can create a new project and invite team members"

# Analyze test failures
auto-test run --analyze-failures
```

## 🎨 Templates

### Atomic Operations
Reusable single operations:

```yaml
templates:
  login:
    type: "atom"
    parameters:
      - name: "email"
        type: "string"
        required: true
    steps:
      - fill '[data-testid="email"]' with "{{ email }}"
      - fill '[data-testid="password"]' with "{{ password }}"
      - click '[data-testid="login"]'
```

### CRUD Templates
Complete workflows:

```yaml
templates:
  crud-workflow:
    type: "crud"
    parameters:
      - name: "entity"
        type: "string"
      - name: "data"
        type: "object"
    steps:
      # Create, Read, Update, Delete operations
```

### Module Templates
Business logic templates:

```yaml
templates:
  user-management:
    type: "module"
    description: "Complete user management workflow"
    # Complex multi-step workflows
```

## 🌍 Environments

Configure different environments:

```yaml
# configs/environments/staging.yml
baseUrl: "https://staging.myapp.com"
variables:
  environment: "staging"
  debug: false
  
testUsers:
  admin:
    email: "<EMAIL>"
    password: "staging_pass"
```

```bash
# Run tests against staging
auto-test run --env staging
```

## 📊 Reporting

Multiple reporting formats:

```bash
# HTML report (default)
auto-test run --reporter html

# JSON report for CI/CD
auto-test run --reporter json

# JUnit for test management tools
auto-test run --reporter junit

# Multiple reporters
auto-test run --reporter html,json,junit
```

## 🔍 Debugging

### Debug Mode
```bash
# Run with debug information
auto-test run --debug

# Run with browser visible
auto-test run --headless false

# Run with slow motion
auto-test run --slow-mo 500
```

### Screenshots and Videos
```yaml
config:
  capture:
    screenshots: true
    videos: true
    screenshotOnFailure: true
```

## 🚀 CI/CD Integration

### GitHub Actions
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: auto-test run --env ci --reporter junit
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: test-results
          path: test-results/
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npx playwright install --with-deps
CMD ["auto-test", "run", "--env", "docker"]
```

## 📚 Examples

Check out the `examples/` directory for complete sample projects:

- **React Admin**: Complete admin dashboard testing
- **Vue E-commerce**: E-commerce site testing
- **Vanilla CRM**: Plain JavaScript CRM testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Documentation](https://automation-testing-framework.dev/docs)
- 💬 [Discord Community](https://discord.gg/automation-testing)
- 🐛 [Issue Tracker](https://github.com/your-org/automation-testing-framework/issues)
- 📧 [Email Support](mailto:<EMAIL>)

## 🗺️ Roadmap

- ✅ YAML DSL Engine
- ✅ AST Injection
- ✅ Basic Templates
- 🚧 LLM Integration
- 🚧 Visual Testing
- 🚧 Mobile Testing
- 📋 API Testing Enhancement
- 📋 Performance Testing
- 📋 Accessibility Testing

---

Built with ❤️ by the Automation Testing Framework team 