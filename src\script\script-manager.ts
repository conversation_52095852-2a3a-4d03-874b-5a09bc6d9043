/**
 * 脚本管理器
 * 统一管理脚本的加载、注册和执行
 */

import { ScriptLoader } from './script-loader';
import { ScriptRegistry } from './script-registry';
import { ScriptExecutor } from './script-executor';
import { PathResolver } from '../utils/path-resolver';
import type {
  ScriptExecutionContext,
  ScriptExecutionResult,
  ScriptLoadResult
} from './types';
import { logger } from '../utils/logger';

export class ScriptManager {
  private registry: ScriptRegistry;
  private executor: ScriptExecutor;
  private pathResolver: PathResolver;
  private isInitialized = false;

  constructor() {
    this.pathResolver = PathResolver.getInstance();
    this.registry = new ScriptRegistry();
    this.executor = new ScriptExecutor(this.registry);

    logger.debug('ScriptManager 初始化', {
      scriptsDir: this.pathResolver.getScriptsDir(),
      pathConfig: this.pathResolver.getPathConfig()
    });
  }

  /**
   * 初始化脚本系统
   */
  async initialize(scriptDirs?: string[]): Promise<ScriptLoadResult> {
    try {
      // 如果没有提供脚本目录，使用默认的脚本目录
      const dirsToScan = scriptDirs || [this.pathResolver.getScriptsDir()];

      logger.info('开始初始化脚本系统', {
        scriptDirs: dirsToScan,
        defaultScriptsDir: this.pathResolver.getScriptsDir()
      });

      // 加载脚本（使用静态方法）
      const loadResult = await ScriptLoader.loadScriptsStatic(dirsToScan);

      
      // 注册脚本
      const registrationErrors: string[] = [];
      for (const script of loadResult.scripts) {
        const registerResult = this.registry.register(script);
        if (!registerResult.isValid) {
          const errorMsg = `注册脚本 ${script.name} 失败: ${registerResult.errors.map(e => e.message).join('; ')}`;
          registrationErrors.push(errorMsg);
        }
      }

      // 合并错误
      const allErrors = [...loadResult.errors, ...registrationErrors];

      // 获取统计信息
      const stats = this.registry.getStatistics();
      
      logger.info('脚本系统初始化完成', {
        loadedScripts: loadResult.scripts.length,
        registeredScripts: stats.totalScripts,
        errorCount: allErrors.length,
        scriptNames: stats.scriptNames
      });

      this.isInitialized = true;

      return {
        success: allErrors.length === 0,
        scripts: loadResult.scripts,
        errors: allErrors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('脚本系统初始化失败', { error: errorMessage });
      
      return {
        success: false,
        scripts: [],
        errors: [`初始化失败: ${errorMessage}`]
      };
    }
  }

  /**
   * 执行脚本
   */
  async executeScript(
    scriptName: string,
    parameters: any = {},
    context: ScriptExecutionContext,
    timeout: number = 30000
  ): Promise<ScriptExecutionResult> {
    if (!this.isInitialized) {
      logger.error('脚本系统未初始化');
      return {
        success: false,
        error: '脚本系统未初始化，请先调用 initialize() 方法',
        duration: 0
      };
    }

    return this.executor.executeScript(scriptName, parameters, context, timeout);
  }

  /**
   * 检查脚本是否存在
   */
  hasScript(scriptName: string): boolean {
    return this.registry.hasScript(scriptName);
  }

  /**
   * 获取所有脚本名称
   */
  getScriptNames(): string[] {
    return this.registry.getScriptNames();
  }

  /**
   * 获取脚本统计信息
   */
  getStatistics() {
    return {
      isInitialized: this.isInitialized,
      ...this.registry.getStatistics(),
      ...this.executor.getExecutionStatistics()
    };
  }

  /**
   * 验证脚本参数
   */
  validateScriptParameters(scriptName: string, parameters: any) {
    return this.registry.validateScriptParameters(scriptName, parameters);
  }

  /**
   * 获取脚本元数据
   */
  getScriptMetadata(scriptName: string) {
    const script = this.registry.getScript(scriptName);
    return script?.metadata;
  }

  /**
   * 根据标签获取脚本
   */
  getScriptsByTag(tag: string) {
    return this.registry.getScriptsByTag(tag);
  }

  /**
   * 重新加载脚本（开发模式）
   */
  async reload(scriptDirs?: string[]): Promise<ScriptLoadResult> {
    logger.info('重新加载脚本系统');

    // 清空现有注册
    this.registry.clear();
    this.isInitialized = false;

    // 重新初始化
    return this.initialize(scriptDirs);
  }

  /**
   * 获取路径解析器
   */
  getPathResolver(): PathResolver {
    return this.pathResolver;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.registry.clear();
    this.isInitialized = false;
    logger.info('脚本系统资源已清理');
  }
}
