import { logger } from '../utils/logger';
import { PathResolver } from '../utils/path-resolver';
import { DSLParser } from '../dsl/parser';
import type {
  EngineOptions,
  TestOptions,
  ResolvedConfig,
  ExecutorType,
  ConnectionType
} from './types';
import type { ValidationResult } from '../validation/yaml-validator';

/**
 * 统一配置服务
 * 支持：默认配置 -> .env 文件 -> 外部传入 -> YAML 配置 -> 运行时选项
 * 整合了原 ConfigService 和 UnifiedConfigService 的功能
 */
export class ConfigService {
  private baseConfig: Required<EngineOptions>;
  private pathResolver: PathResolver;

  constructor(options: EngineOptions = {}) {
    // 初始化路径解析器（会自动加载 .env 文件）
    this.pathResolver = PathResolver.getInstance();

    // 1. 系统默认配置
    const defaults: Required<EngineOptions> = {
      executorType: 'web',
      headless: true,
      slowMo: 0,
      timeout: 30000,
      connectionType: 'new-browser',
      cdpEndpoint: '',
      debuggingPort: 9222,
      autoPort: true,
      outputDir: this.pathResolver.getOutputDir(),
      templateDirs: [this.pathResolver.getTemplatesDir()],
      captureScreenshots: false,
      continueOnFailure: false,
      forceCleanupOnFailure: true,
    };

    // 2. 从环境变量加载配置（使用 dotenv 已加载的环境变量）
    const envConfig = this.loadEnvConfig();

    // 3. 合并外部传入配置
    this.baseConfig = {
      ...defaults,
      ...envConfig,
      ...options
    };

    logger.info('统一配置服务初始化完成', {
      config: this.baseConfig,
      pathConfig: this.pathResolver.getPathConfig(),
      source: 'defaults + env + options'
    });
  }
  
  /**
   * 获取基础配置
   */
  getBaseConfig(): Required<EngineOptions> {
    return { ...this.baseConfig };
  }

  /**
   * 获取路径解析器
   */
  getPathResolver(): PathResolver {
    return this.pathResolver;
  }

  /**
   * 获取路径配置
   */
  getPathConfig() {
    return this.pathResolver.getPathConfig();
  }
  
  /**
   * 合并 YAML 配置和运行时选项，生成最终配置
   */
  resolveConfig(yamlConfig: any = {}, testOptions: TestOptions = {}): ResolvedConfig {
    const resolved: ResolvedConfig = {
      // 基础配置
      executorType: yamlConfig.executorType || (testOptions as any).executorType || this.baseConfig.executorType,
      headless: this.baseConfig.headless,
      slowMo: this.baseConfig.slowMo,
      timeout: testOptions.timeout || yamlConfig.timeout || this.baseConfig.timeout,
      
      // 连接配置
      connectionType: this.baseConfig.connectionType,
      cdpEndpoint: this.baseConfig.cdpEndpoint || undefined,
      debuggingPort: this.baseConfig.debuggingPort,
      autoPort: this.baseConfig.autoPort,
      
      // 测试配置
      outputDir: testOptions.outputDir || this.baseConfig.outputDir,
      templateDirs: this.baseConfig.templateDirs,
      captureScreenshots: testOptions.captureScreenshots ?? yamlConfig.captureScreenshots ?? this.baseConfig.captureScreenshots,
      continueOnFailure: testOptions.continueOnFailure ?? yamlConfig.continueOnFailure ?? this.baseConfig.continueOnFailure,
      forceCleanupOnFailure: testOptions.forceCleanupOnFailure ?? yamlConfig.forceCleanupOnFailure ?? this.baseConfig.forceCleanupOnFailure,
      
      // YAML 特有配置
      baseUrl: yamlConfig.baseUrl,
      variables: yamlConfig.variables || {},
    };
    
    logger.debug('配置解析完成', { 
      resolved,
      sources: {
        yaml: !!yamlConfig.name,
        testOptions: Object.keys(testOptions).length > 0
      }
    });
    
    return resolved;
  }

  /**
   * 转换为 BrowserOptions（向后兼容）
   */
  toBrowserOptions(resolved?: ResolvedConfig): any {
    const config = resolved || this.resolveConfig();

    return {
      browser: 'chromium' as const,
      headless: config.headless,
      slowMo: config.slowMo,
      timeout: config.timeout,
      cdpEndpoint: config.cdpEndpoint,
      debuggingPort: config.debuggingPort,
      autoPort: config.autoPort,
      enableCDP: config.connectionType === 'cdp',
    };
  }

  /**
   * 解析和验证配置（从 UnifiedConfigService 迁移）
   * 统一的配置处理入口
   */
  async resolveAndValidateConfig(
    yamlConfig?: any,
    runtimeOptions?: any
  ): Promise<{
    config: ResolvedConfig;
    validation: ValidationResult;
  }> {
    logger.debug('开始解析和验证配置', {
      hasYamlConfig: !!yamlConfig,
      hasRuntimeOptions: !!runtimeOptions,
    });

    try {
      // 1. 解析配置
      const resolvedConfig = this.resolveConfig(yamlConfig, runtimeOptions);

      // 2. 验证配置
      const validation = DSLParser.validateConfig(resolvedConfig);

      // 3. 记录配置解析结果
      logger.info('配置解析和验证完成', {
        isValid: validation.isValid,
        errorCount: validation.errors.length,
        warningCount: validation.warnings.length,
        executorType: resolvedConfig.executorType,
        connectionType: resolvedConfig.connectionType,
      });

      return {
        config: resolvedConfig,
        validation,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('配置解析失败', { error: errorMessage });

      // 返回默认配置和错误验证结果
      const defaultConfig = this.getBaseConfig();
      const validation: ValidationResult = {
        isValid: false,
        errors: [
          {
            type: 'STRUCTURE',
            severity: 'ERROR',
            message: `配置解析失败: ${errorMessage}`,
            suggestions: ['检查配置格式是否正确'],
          },
        ],
        warnings: [],
      };

      return {
        config: defaultConfig,
        validation,
      };
    }
  }

  /**
   * 验证YAML内容（从 UnifiedConfigService 迁移）
   * 委托给DSLParser
   */
  async validateYaml(yamlContent: string): Promise<ValidationResult> {
    const result = DSLParser.parseWithValidation(yamlContent);
    return {
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
    };
  }

  /**
   * 清理资源（从 UnifiedConfigService 迁移）
   */
  dispose(): void {
    // 清理配置服务相关资源
    logger.debug('ConfigService 资源清理完成');
  }

  /**
   * 从环境变量加载配置（使用 dotenv 已加载的环境变量）
   */
  private loadEnvConfig(): Partial<EngineOptions> {
    const envConfig: Partial<EngineOptions> = {};

    try {
      
      // 直接从 process.env 读取环境变量（已通过 dotenv 加载）
      if (process.env.EXECUTOR_TYPE) {
        envConfig.executorType = process.env.EXECUTOR_TYPE as ExecutorType;
      }
      if (process.env.HEADLESS) {
        envConfig.headless = process.env.HEADLESS.toLowerCase() === 'true';
      }
      if (process.env.SLOW_MO) {
        const slowMo = Number.parseInt(process.env.SLOW_MO, 10);
        if (!Number.isNaN(slowMo)) envConfig.slowMo = slowMo;
      }
      if (process.env.TIMEOUT) {
        const timeout = Number.parseInt(process.env.TIMEOUT, 10);
        if (!Number.isNaN(timeout)) envConfig.timeout = timeout;
      }
      if (process.env.CONNECTION_TYPE) {
        envConfig.connectionType = process.env.CONNECTION_TYPE as ConnectionType;
      }
      if (process.env.CDP_ENDPOINT) {
        envConfig.cdpEndpoint = process.env.CDP_ENDPOINT;
      }
      if (process.env.DEBUGGING_PORT) {
        const port = Number.parseInt(process.env.DEBUGGING_PORT, 10);
        if (!Number.isNaN(port)) envConfig.debuggingPort = port;
      }
      if (process.env.OUTPUT_DIR || process.env.AUTOMATION_OUTPUT_DIR) {
        // 支持新的环境变量名，同时保持向后兼容
        const outputDir = process.env.AUTOMATION_OUTPUT_DIR || process.env.OUTPUT_DIR;
        if (outputDir) {
          envConfig.outputDir = this.pathResolver.resolve(outputDir);
        }
      }
      if (process.env.CAPTURE_SCREENSHOTS) {
        envConfig.captureScreenshots = process.env.CAPTURE_SCREENSHOTS.toLowerCase() === 'true';
      }

      // 新增：模板目录配置支持
      if (process.env.AUTOMATION_TEMPLATES_DIR) {
        envConfig.templateDirs = [this.pathResolver.resolve(process.env.AUTOMATION_TEMPLATES_DIR)];
      }

      logger.debug('从环境变量加载配置', { envConfig });

    } catch (error) {
      logger.warn('加载环境变量配置失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    return envConfig;
  }
}
