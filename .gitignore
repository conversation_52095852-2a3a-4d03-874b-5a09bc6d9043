# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
out/
coverage/

# Test results
test-results/
playwright-report/
.nyc_output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Cache directories
.cache/
.npm/
.eslintcache
.parcel-cache/

# Temporary folders
tmp/
temp/

# Local configuration
config/local.json
config/local.yml 