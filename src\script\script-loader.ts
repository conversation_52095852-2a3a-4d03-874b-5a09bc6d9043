/**
 * 脚本加载器
 * 从文件系统加载JavaScript脚本文件
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { pathToFileURL } from 'node:url';
import type { ScriptDefinition, ScriptLoadResult, ScriptMetadata } from './types';
import { logger } from '../utils/logger';

export class ScriptLoader {
  /**
   * 从指定目录加载所有脚本
   */
  async loadScripts(scriptDirs: string[]): Promise<ScriptLoadResult> {
    const scripts: ScriptDefinition[] = [];
    const errors: string[] = [];

    logger.info('开始加载脚本', { scriptDirs });

    for (const dir of scriptDirs) {
      try {
        const dirScripts = await this.loadScriptsFromDirectory(dir);
        scripts.push(...dirScripts.scripts);
        errors.push(...dirScripts.errors);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('加载脚本目录失败', { dir, error: errorMessage });
        errors.push(`加载目录 ${dir} 失败: ${errorMessage}`);
      }
    }

    logger.info('脚本加载完成', { 
      totalScripts: scripts.length, 
      errorCount: errors.length 
    });

    return {
      success: errors.length === 0,
      scripts,
      errors
    };
  }

  /**
   * 从单个目录加载脚本
   */
  private async loadScriptsFromDirectory(dir: string): Promise<ScriptLoadResult> {
    const scripts: ScriptDefinition[] = [];
    const errors: string[] = [];

    if (!fs.existsSync(dir)) {
      logger.warn('脚本目录不存在', { dir });
      return { success: true, scripts: [], errors: [] };
    }

    const files = await this.findScriptFiles(dir);
    
    for (const filePath of files) {
      try {
        const script = await this.loadScriptFile(filePath);
        if (script) {
          scripts.push(script);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('加载脚本文件失败', { filePath, error: errorMessage });
        errors.push(`加载文件 ${filePath} 失败: ${errorMessage}`);
      }
    }

    return {
      success: errors.length === 0,
      scripts,
      errors
    };
  }

  /**
   * 递归查找所有JavaScript文件
   */
  private async findScriptFiles(dir: string): Promise<string[]> {
    const files: string[] = [];
    
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // 递归查找子目录
        const subFiles = await this.findScriptFiles(fullPath);
        files.push(...subFiles);
      } else if (entry.isFile() && this.isScriptFile(entry.name)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * 检查是否为脚本文件
   */
  private isScriptFile(fileName: string): boolean {
    return fileName.endsWith('.js') || fileName.endsWith('.mjs');
  }

  /**
   * 加载单个脚本文件
   */
  private async loadScriptFile(filePath: string): Promise<ScriptDefinition | null> {
    try {
      logger.debug('加载脚本文件', { filePath });

      // 使用动态导入加载ES模块
      const fileUrl = pathToFileURL(filePath).href;
      const module = await import(fileUrl);

      // 检查是否有默认导出函数
      if (typeof module.default !== 'function') {
        logger.warn('脚本文件缺少默认导出函数', { filePath });
        return null;
      }

      // 检查是否有元数据
      if (!module.metadata || typeof module.metadata !== 'object') {
        logger.warn('脚本文件缺少元数据', { filePath });
        return null;
      }

      const metadata: ScriptMetadata = module.metadata;
      
      // 如果元数据中没有name，使用文件名
      if (!metadata.name) {
        metadata.name = this.getScriptNameFromPath(filePath);
      }

      const script: ScriptDefinition = {
        name: metadata.name,
        filePath,
        execute: module.default,
        metadata
      };

      logger.debug('脚本文件加载成功', { 
        scriptName: script.name, 
        filePath,
        hasParameters: !!(metadata.parameters && Object.keys(metadata.parameters).length > 0)
      });

      return script;

    } catch (error) {
      logger.error('加载脚本文件异常', { 
        filePath, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * 从文件路径提取脚本名称
   */
  private getScriptNameFromPath(filePath: string): string {
    const fileName = path.basename(filePath, path.extname(filePath));
    // 将文件名转换为有效的脚本名称（移除特殊字符）
    return fileName.replace(/[^a-zA-Z0-9-_]/g, '-');
  }

  /**
   * 验证脚本文件是否可访问
   */
  static async validateScriptPath(scriptPath: string): Promise<boolean> {
    try {
      const stats = fs.statSync(scriptPath);
      return stats.isFile();
    } catch {
      return false;
    }
  }

  /**
   * 获取脚本文件的修改时间
   */
  static getScriptModificationTime(scriptPath: string): Date | null {
    try {
      const stats = fs.statSync(scriptPath);
      return stats.mtime;
    } catch {
      return null;
    }
  }

  /**
   * 静态方法：从指定目录加载所有脚本
   * 提供给其他模块复用脚本加载逻辑
   */
  static async loadScriptsStatic(scriptDirs: string[]): Promise<ScriptLoadResult> {
    const loader = new ScriptLoader();
    return loader.loadScripts(scriptDirs);
  }
}
