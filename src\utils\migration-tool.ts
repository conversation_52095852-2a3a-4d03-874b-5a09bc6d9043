/**
 * 配置迁移工具
 * 帮助用户从现有配置迁移到新的 .automation 目录结构
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { PathResolver } from './path-resolver';
import { logger } from './logger';

export interface MigrationOptions {
  /** 是否执行实际迁移（false 为预览模式） */
  dryRun?: boolean;
  /** 是否备份原文件 */
  backup?: boolean;
  /** 是否强制覆盖已存在的文件 */
  force?: boolean;
  /** 自定义项目根目录 */
  rootDir?: string;
}

export interface MigrationResult {
  success: boolean;
  actions: MigrationAction[];
  errors: string[];
  warnings: string[];
}

export interface MigrationAction {
  type: 'copy' | 'move' | 'create' | 'backup';
  source?: string;
  target: string;
  description: string;
  executed: boolean;
  error?: string;
}

/**
 * 配置迁移工具类
 */
export class MigrationTool {
  private pathResolver: PathResolver;
  private options: Required<MigrationOptions>;

  constructor(options: MigrationOptions = {}) {
    this.options = {
      dryRun: options.dryRun ?? true,
      backup: options.backup ?? true,
      force: options.force ?? false,
      rootDir: options.rootDir || process.cwd(),
    };

    this.pathResolver = PathResolver.getInstance({
      rootDir: this.options.rootDir,
      enableLegacyMode: true,
    });
  }

  /**
   * 执行迁移
   */
  async migrate(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      actions: [],
      errors: [],
      warnings: [],
    };

    try {
      logger.info('开始配置迁移', {
        dryRun: this.options.dryRun,
        rootDir: this.options.rootDir,
      });

      // 1. 检查现有结构
      const analysis = this.analyzeCurrentStructure();
      
      // 2. 创建迁移计划
      const plan = this.createMigrationPlan(analysis);
      
      // 3. 执行迁移计划
      for (const action of plan) {
        try {
          const executed = await this.executeAction(action);
          result.actions.push({ ...action, executed });
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error);
          result.errors.push(`执行操作失败: ${action.description} - ${errorMsg}`);
          result.actions.push({ ...action, executed: false, error: errorMsg });
          result.success = false;
        }
      }

      // 4. 生成报告
      this.generateReport(result);

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      result.errors.push(`迁移过程失败: ${errorMsg}`);
      result.success = false;
    }

    return result;
  }

  /**
   * 分析当前项目结构
   */
  private analyzeCurrentStructure() {
    const rootDir = this.pathResolver.getRootDir();
    
    return {
      hasLegacyTemplates: fs.existsSync(path.join(rootDir, 'templates')),
      hasLegacyScripts: fs.existsSync(path.join(rootDir, 'scripts')),
      hasLegacyEnv: fs.existsSync(path.join(rootDir, '.env')),
      hasNewStructure: fs.existsSync(path.join(rootDir, '.automation')),
      legacyTemplatesPath: path.join(rootDir, 'templates'),
      legacyScriptsPath: path.join(rootDir, 'scripts'),
      legacyEnvPath: path.join(rootDir, '.env'),
    };
  }

  /**
   * 创建迁移计划
   */
  private createMigrationPlan(analysis: any): MigrationAction[] {
    const actions: MigrationAction[] = [];
    const pathConfig = this.pathResolver.getPathConfig();

    // 创建 .automation 目录结构
    if (!analysis.hasNewStructure) {
      actions.push({
        type: 'create',
        target: pathConfig.templatesDir,
        description: '创建模板目录',
        executed: false,
      });

      actions.push({
        type: 'create',
        target: pathConfig.scriptsDir,
        description: '创建脚本目录',
        executed: false,
      });

      actions.push({
        type: 'create',
        target: pathConfig.schemasDir,
        description: '创建模式目录',
        executed: false,
      });
    }

    // 迁移模板文件
    if (analysis.hasLegacyTemplates) {
      actions.push({
        type: 'copy',
        source: analysis.legacyTemplatesPath,
        target: pathConfig.templatesDir,
        description: '迁移模板文件',
        executed: false,
      });
    }

    // 迁移脚本文件
    if (analysis.hasLegacyScripts) {
      actions.push({
        type: 'copy',
        source: analysis.legacyScriptsPath,
        target: pathConfig.scriptsDir,
        description: '迁移脚本文件',
        executed: false,
      });
    }

    // 迁移环境配置（现在直接在根目录，无需迁移）
    if (analysis.hasLegacyEnv) {
      // .env 文件已经在根目录，无需迁移
      // 只需要提醒用户检查配置
      actions.push({
        type: 'create',
        target: path.join(pathConfig.rootDir, '.env.backup'),
        description: '备份现有 .env 文件（配置已在根目录，无需迁移）',
        executed: false,
      });
    }

    return actions;
  }

  /**
   * 执行单个迁移操作
   */
  private async executeAction(action: MigrationAction): Promise<boolean> {
    if (this.options.dryRun) {
      logger.info(`[预览] ${action.description}`, {
        type: action.type,
        source: action.source,
        target: action.target,
      });
      return true;
    }

    switch (action.type) {
      case 'create':
        return this.createDirectory(action.target);
      
      case 'copy':
        return this.copyPath(action.source!, action.target);
      
      case 'move':
        return this.movePath(action.source!, action.target);
      
      case 'backup':
        return this.backupPath(action.source!);
      
      default:
        throw new Error(`未知的操作类型: ${action.type}`);
    }
  }

  /**
   * 创建目录
   */
  private createDirectory(dirPath: string): boolean {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info('创建目录', { path: dirPath });
    }
    return true;
  }

  /**
   * 复制文件或目录
   */
  private copyPath(source: string, target: string): boolean {
    if (!fs.existsSync(source)) {
      throw new Error(`源路径不存在: ${source}`);
    }

    if (fs.existsSync(target) && !this.options.force) {
      throw new Error(`目标路径已存在: ${target}`);
    }

    // 确保目标目录存在
    const targetDir = path.dirname(target);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // 复制文件或目录
    this.copyRecursive(source, target);
    logger.info('复制完成', { source, target });
    
    return true;
  }

  /**
   * 移动文件或目录
   */
  private movePath(source: string, target: string): boolean {
    this.copyPath(source, target);
    fs.rmSync(source, { recursive: true, force: true });
    logger.info('移动完成', { source, target });
    return true;
  }

  /**
   * 备份文件或目录
   */
  private backupPath(source: string): boolean {
    if (!fs.existsSync(source)) {
      return true;
    }

    const backupPath = `${source}.backup.${Date.now()}`;
    this.copyRecursive(source, backupPath);
    logger.info('备份完成', { source, backup: backupPath });
    
    return true;
  }

  /**
   * 递归复制文件或目录
   */
  private copyRecursive(source: string, target: string): void {
    const stats = fs.statSync(source);
    
    if (stats.isDirectory()) {
      if (!fs.existsSync(target)) {
        fs.mkdirSync(target, { recursive: true });
      }
      
      const items = fs.readdirSync(source);
      for (const item of items) {
        const sourcePath = path.join(source, item);
        const targetPath = path.join(target, item);
        this.copyRecursive(sourcePath, targetPath);
      }
    } else {
      fs.copyFileSync(source, target);
    }
  }

  /**
   * 生成迁移报告
   */
  private generateReport(result: MigrationResult): void {
    const mode = this.options.dryRun ? '预览模式' : '执行模式';
    
    logger.info(`迁移完成 (${mode})`, {
      success: result.success,
      totalActions: result.actions.length,
      executedActions: result.actions.filter(a => a.executed).length,
      errors: result.errors.length,
      warnings: result.warnings.length,
    });

    if (result.errors.length > 0) {
      logger.error('迁移错误', { errors: result.errors });
    }

    if (result.warnings.length > 0) {
      logger.warn('迁移警告', { warnings: result.warnings });
    }
  }

  /**
   * 创建迁移工具实例并执行迁移
   */
  static async runMigration(options: MigrationOptions = {}): Promise<MigrationResult> {
    const tool = new MigrationTool(options);
    return tool.migrate();
  }
}
