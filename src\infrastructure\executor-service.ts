import { logger } from '../utils/logger';
import type { ExecutorType, TestStep } from '../types';
import type { ExecutionContext } from '../testing-engine/execution-engine';
import { WebExecutor } from '../execution/executors/web-executor';
import { ElementPlusExecutor } from '../execution/executors/element-plus-executor';

interface ExecutionOptions {
  timeout?: number;
  retries?: number;
  continueOnFailure?: boolean;
  captureScreenshots?: boolean;
  executorType?: ExecutorType;
}

/**
 * 执行器配置
 */
export interface ExecutorConfig {
  /** 执行器类型 */
  type: ExecutorType;
}

/**
 * 执行器注册信息
 */
export interface ExecutorRegistration {
  /** 执行器类型 */
  type: ExecutorType;
  /** 执行器类构造函数 */
  executorClass: new () => any;
  /** 执行器描述 */
  description: string;
  /** 是否为默认执行器 */
  isDefault?: boolean;
}

/**
 * 执行器状态
 */
export interface ExecutorStatus {
  /** 执行器类型 */
  type: ExecutorType;
  /** 是否可用 */
  available: boolean;
  /** 当前运行的测试数量 */
  runningTests: number;
  /** 执行器实例是否已创建 */
  initialized: boolean;
  /** 最后使用时间 */
  lastUsed?: Date;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 执行器服务状态
 */
export interface ExecutorServiceStatus {
  /** 是否已初始化 */
  initialized: boolean;
  /** 当前活跃的执行器类型 */
  activeExecutorType: ExecutorType;
  /** 已注册的执行器数量 */
  registeredExecutors: number;
  /** 所有执行器状态 */
  executors: Record<ExecutorType, ExecutorStatus>;
  /** 总运行测试数 */
  totalRunningTests: number;
}

/**
 * 执行器选择策略
 */
export interface ExecutorSelectionStrategy {
  /** 策略名称 */
  name: string;
  /** 选择执行器的逻辑 */
  selectExecutor(
    availableExecutors: ExecutorType[],
    context: ExecutionContext,
    step?: TestStep
  ): ExecutorType;
}

/**
 * ExecutorService 接口
 * 统一的执行器管理服务，负责执行器注册、发现、调度和生命周期管理
 */
export interface IExecutorService {
  /**
   * 初始化执行器服务
   */
  initialize(config?: ExecutorConfig): Promise<void>;

  /**
   * 注册执行器
   */
  registerExecutor(registration: ExecutorRegistration): void;

  /**
   * 注销执行器
   */
  unregisterExecutor(type: ExecutorType): void;

  /**
   * 获取执行器实例
   */
  getExecutor(type?: ExecutorType): any;

  /**
   * 设置默认执行器类型
   */
  setDefaultExecutorType(type: ExecutorType): void;

  /**
   * 获取默认执行器类型
   */
  getDefaultExecutorType(): ExecutorType;

  /**
   * 获取所有已注册的执行器类型
   */
  getRegisteredExecutorTypes(): ExecutorType[];

  /**
   * 检查执行器是否可用
   */
  isExecutorAvailable(type: ExecutorType): boolean;

  /**
   * 执行单个步骤
   */
  executeStep(
    step: TestStep,
    context: ExecutionContext,
    executorType?: ExecutorType
  ): Promise<void>;

  /**
   * 执行完整测试
   */
  executeTest(
    testId: string,
    steps: TestStep[],
    context: ExecutionContext,
    options?: ExecutionOptions
  ): Promise<void>;

  /**
   * 获取执行器状态
   */
  getExecutorStatus(type: ExecutorType): ExecutorStatus;

  /**
   * 获取服务状态
   */
  getServiceStatus(): ExecutorServiceStatus;

  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}

/**
 * ExecutorService 实现类
 * 统一管理所有执行器，提供智能调度和生命周期管理
 */
export class ExecutorService implements IExecutorService {
  private registeredExecutors: Map<ExecutorType, ExecutorRegistration> = new Map();
  private executorInstances: Map<ExecutorType, any> = new Map();
  private defaultExecutorType: ExecutorType = 'web';
  private isInitialized = false;
  private selectionStrategy: ExecutorSelectionStrategy;

  constructor() {
    // 设置默认选择策略
    this.selectionStrategy = new DefaultExecutorSelectionStrategy();

    logger.debug('ExecutorService 创建完成');
  }

  /**
   * 初始化执行器服务
   */
  async initialize(config?: ExecutorConfig): Promise<void> {
    if (this.isInitialized) {
      logger.warn('ExecutorService 已经初始化，跳过重复初始化');
      return;
    }

    logger.info('开始初始化 ExecutorService', config);

    try {
      // 注册内置执行器
      await this.registerBuiltinExecutors();

      // 设置默认执行器类型
      if (config?.type) {
        this.setDefaultExecutorType(config.type);
      }

      // 创建默认执行器实例
      this.ensureExecutorInstance(this.defaultExecutorType);

      this.isInitialized = true;

      logger.info('ExecutorService 初始化完成', {
        defaultExecutorType: this.defaultExecutorType,
        registeredExecutors: Array.from(this.registeredExecutors.keys()),
        initializedExecutors: Array.from(this.executorInstances.keys())
      });

    } catch (error) {
      logger.error('ExecutorService 初始化失败', {
        error: error instanceof Error ? error.message : String(error),
      });

      // 清理失败的初始化
      await this.cleanup();
      throw error;
    }
  }

  /**
   * 注册执行器
   */
  registerExecutor(registration: ExecutorRegistration): void {
    logger.debug('注册执行器', {
      type: registration.type,
      description: registration.description,
      isDefault: registration.isDefault
    });

    this.registeredExecutors.set(registration.type, registration);

    // 如果是默认执行器，更新默认类型
    if (registration.isDefault) {
      this.defaultExecutorType = registration.type;
    }

    logger.info('执行器注册成功', {
      type: registration.type,
      totalRegistered: this.registeredExecutors.size
    });
  }

  /**
   * 注销执行器
   */
  unregisterExecutor(type: ExecutorType): void {
    logger.debug('注销执行器', { type });

    // 清理执行器实例
    const instance = this.executorInstances.get(type);
    if (instance && typeof instance.cleanup === 'function') {
      instance.cleanup().catch((error: any) => {
        logger.warn('执行器实例清理失败', {
          type,
          error: error instanceof Error ? error.message : String(error)
        });
      });
    }

    this.executorInstances.delete(type);
    this.registeredExecutors.delete(type);

    // 如果注销的是默认执行器，选择新的默认执行器
    if (this.defaultExecutorType === type) {
      const availableTypes = Array.from(this.registeredExecutors.keys());
      this.defaultExecutorType = availableTypes.length > 0 ? availableTypes[0] : 'web';
    }

    logger.info('执行器注销成功', {
      type,
      newDefaultType: this.defaultExecutorType,
      remainingExecutors: this.registeredExecutors.size
    });
  }

  /**
   * 获取执行器实例
   */
  getExecutor(type?: ExecutorType): any {
    const executorType = type || this.defaultExecutorType;

    if (!this.isExecutorAvailable(executorType)) {
      throw new Error(`执行器 '${executorType}' 未注册或不可用`);
    }

    // 懒加载执行器实例
    if (!this.executorInstances.has(executorType)) {
      this.createExecutorInstance(executorType);
    }

    return this.executorInstances.get(executorType)!;
  }

  /**
   * 设置默认执行器类型
   */
  setDefaultExecutorType(type: ExecutorType): void {
    if (!this.registeredExecutors.has(type)) {
      throw new Error(`执行器类型 '${type}' 未注册`);
    }

    this.defaultExecutorType = type;

    logger.info('默认执行器类型已更新', { type });
  }

  /**
   * 获取默认执行器类型
   */
  getDefaultExecutorType(): ExecutorType {
    return this.defaultExecutorType;
  }

  /**
   * 获取所有已注册的执行器类型
   */
  getRegisteredExecutorTypes(): ExecutorType[] {
    return Array.from(this.registeredExecutors.keys());
  }

  /**
   * 检查执行器是否可用
   */
  isExecutorAvailable(type: ExecutorType): boolean {
    return this.registeredExecutors.has(type);
  }

  /**
   * 执行单个步骤
   */
  async executeStep(
    step: TestStep,
    context: ExecutionContext,
    executorType?: ExecutorType
  ): Promise<void> {
    // 选择合适的执行器
    const selectedType = executorType ||
      this.selectionStrategy.selectExecutor(
        this.getRegisteredExecutorTypes(),
        context,
        step
      );

    const executor = this.getExecutor(selectedType);

    logger.debug('执行单个步骤', {
      stepId: step.id,
      action: step.action,
      executorType: selectedType
    });

    // 执行步骤并检查结果
    const stepResult = await executor.executeStep(step, context);

    // 检查步骤执行结果，如果失败则抛出异常
    if (stepResult && stepResult.status === 'failed') {
      const errorMessage = stepResult.error || '步骤执行失败';
      logger.error('ExecutorService: 步骤执行失败', {
        stepId: step.id,
        action: step.action,
        executorType: selectedType,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  }

  /**
   * 执行完整测试
   */
  async executeTest(
    testId: string,
    steps: TestStep[],
    context: ExecutionContext,
    options: ExecutionOptions = {}
  ): Promise<any> {
    // 选择执行器类型
    const executorType = context.config.executorType || this.defaultExecutorType;
    const executor = this.getExecutor(executorType);

    logger.info('ExecutorService 执行测试', {
      testId,
      stepCount: steps.length,
      executorType
    });

    // 执行测试并返回结果
    const result = await executor.executeTest(testId, steps, context, options);


    // 检查测试结果，如果失败则抛出错误
    if (result.status === 'failed') {
      throw new Error(result.error || '测试执行失败');
    }

    return result;
  }

  /**
   * 获取执行器状态
   */
  getExecutorStatus(type: ExecutorType): ExecutorStatus {
    const registration = this.registeredExecutors.get(type);
    const instance = this.executorInstances.get(type);

    return {
      type,
      available: !!registration,
      runningTests: 0, // TODO: 实现运行测试计数
      initialized: !!instance,
      lastUsed: undefined, // TODO: 实现最后使用时间跟踪
    };
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): ExecutorServiceStatus {
    const executors: Record<ExecutorType, ExecutorStatus> = {} as any;

    for (const type of this.getRegisteredExecutorTypes()) {
      executors[type] = this.getExecutorStatus(type);
    }

    return {
      initialized: this.isInitialized,
      activeExecutorType: this.defaultExecutorType,
      registeredExecutors: this.registeredExecutors.size,
      executors,
      totalRunningTests: Object.values(executors).reduce((sum, status) => sum + status.runningTests, 0),
    };
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    logger.info('开始清理 ExecutorService 资源');

    try {
      // 清理所有执行器实例
      for (const [type, instance] of this.executorInstances) {
        if (instance && typeof instance.cleanup === 'function') {
          try {
            await instance.cleanup();
            logger.debug('执行器实例清理完成', { type });
          } catch (error) {
            logger.warn('执行器实例清理失败', {
              type,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }
      }

      // 重置状态
      this.executorInstances.clear();
      this.registeredExecutors.clear();
      this.isInitialized = false;
      this.defaultExecutorType = 'web';

      logger.info('ExecutorService 资源清理完成');

    } catch (error) {
      logger.warn('ExecutorService 清理过程中出现错误', {
        error: error instanceof Error ? error.message : String(error),
      });

      // 即使出错也重置状态
      this.executorInstances.clear();
      this.registeredExecutors.clear();
      this.isInitialized = false;
    }
  }


  // === 私有方法 ===

  /**
   * 注册内置执行器
   */
  private async registerBuiltinExecutors(): Promise<void> {
    logger.debug('注册内置执行器');

    try {

      // 注册 Web 执行器
      this.registerExecutor({
        type: 'web',
        executorClass: WebExecutor,
        description: 'Standard web executor for general web automation',
        isDefault: true,
      });

      // 注册 Element Plus 执行器
      this.registerExecutor({
        type: 'element-plus',
        executorClass: ElementPlusExecutor,
        description: 'Specialized executor for Element Plus components with enhanced interaction support',
      });

      logger.info('内置执行器注册完成', {
        registeredTypes: Array.from(this.registeredExecutors.keys())
      });

    } catch (error) {
      logger.error('内置执行器注册失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 确保执行器实例存在
   */
  private ensureExecutorInstance(type: ExecutorType): void {
    if (!this.executorInstances.has(type)) {
      this.createExecutorInstance(type);
    }
  }

  /**
   * 创建执行器实例
   */
  private createExecutorInstance(type: ExecutorType): void {
    const registration = this.registeredExecutors.get(type);
    if (!registration) {
      throw new Error(`执行器类型 '${type}' 未注册`);
    }

    try {
      const instance = new registration.executorClass();
      this.executorInstances.set(type, instance);

      logger.debug('执行器实例创建成功', {
        type,
        className: registration.executorClass.name
      });

    } catch (error) {
      logger.error('执行器实例创建失败', {
        type,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}

/**
 * 默认执行器选择策略
 * 根据步骤类型和上下文智能选择最合适的执行器
 */
export class DefaultExecutorSelectionStrategy implements ExecutorSelectionStrategy {
  name = 'default';

  selectExecutor(
    availableExecutors: ExecutorType[],
    context: ExecutionContext,
    step?: TestStep
  ): ExecutorType {
    // 如果上下文中指定了执行器类型，优先使用
    if (context.config?.executorType && availableExecutors.includes(context.config.executorType)) {
      return context.config.executorType;
    }

    // 如果步骤中有特定的执行器提示，使用相应执行器
    if (step) {
      // Element Plus 特定动作
      const elementPlusActions = ['selectOption', 'selectDate'];
      if (elementPlusActions.includes(step.action) && availableExecutors.includes('element-plus')) {
        return 'element-plus';
      }

      // 如果使用了 Element Plus 特定的选择器或属性
      if (this.isElementPlusContext(step) && availableExecutors.includes('element-plus')) {
        return 'element-plus';
      }
    }

    // 默认使用 web 执行器
    return availableExecutors.includes('web') ? 'web' : availableExecutors[0];
  }

  /**
   * 判断是否为 Element Plus 上下文
   */
  private isElementPlusContext(step: TestStep): boolean {
    // 检查选择器是否包含 Element Plus 类名
    if (step.selector && step.selector.includes('el-')) {
      return true;
    }

    // 检查 role 是否为 Element Plus 组件
    if (step.role && step.roleOptions?.name) {
      const elementPlusRoles = ['button', 'textbox', 'combobox', 'option'];
      return elementPlusRoles.includes(step.role);
    }

    return false;
  }
}

/**
 * ExecutorService 工厂类
 * 提供简单的创建接口
 */
export class ExecutorServiceFactory {
  /**
   * 创建 ExecutorService 实例
   */
  static async create(config?: ExecutorConfig): Promise<IExecutorService> {
    const service = new ExecutorService();
    await service.initialize(config);
    return service;
  }

  /**
   * 创建智能执行器服务
   * 默认使用 web 执行器，支持智能切换
   */
  static async createSmart(): Promise<IExecutorService> {
    return await ExecutorServiceFactory.create({ type: 'web' });
  }
}

/**
 * 便捷的创建函数
 */
export const createExecutorService = ExecutorServiceFactory.create;
export const createSmartExecutorService = ExecutorServiceFactory.createSmart;