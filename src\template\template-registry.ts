import * as fs from 'node:fs';
import * as path from 'node:path';
import * as yaml from 'yaml';
import { glob } from 'glob';
import { logger } from '../utils/logger';
import { PathResolver } from '../utils/path-resolver';
import { DependencyChecker } from './dependency-checker';

export interface TemplateDefinition {
  id: string;
  name: string;
  type: 'shared' | 'business';
  description?: string;
  module?: string;
  parameters?: TemplateParameter[];
  steps: any[];
  metadata?: {
    author?: string;
    version?: string;
    tags?: string[];
    dependencies?: string[];
    filePath?: string;
    lastModified?: string;
  };
}

export interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description?: string;
  default?: any;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    enum?: any[];
  };
}

export interface TemplateRegistryConfig {
  templateDirs: string[];
  patterns: string[];
  autoScan: boolean;
  cacheEnabled: boolean;
  watchMode: boolean;
  sharedTemplatesDir?: string;
  modulesTemplatesDir?: string;
} 

export class TemplateRegistry {
  private templates: Map<string, TemplateDefinition> = new Map();
  private moduleTemplates: Map<string, TemplateDefinition[]> = new Map();
  private config: TemplateRegistryConfig;
  private watchers: fs.FSWatcher[] = [];
  private dependencyChecker: DependencyChecker;
  private pathResolver: PathResolver;

  constructor(config: Partial<TemplateRegistryConfig> = {}) {
    // 初始化路径解析器
    this.pathResolver = PathResolver.getInstance();

    // 使用路径解析器提供的默认路径
    const defaultTemplatesDir = this.pathResolver.getTemplatesDir();

    this.config = {
      templateDirs: [defaultTemplatesDir],
      patterns: ['**/*.yml', '**/*.yaml'],
      autoScan: true,
      cacheEnabled: true,
      watchMode: false,
      sharedTemplatesDir: path.join(defaultTemplatesDir, 'shared'),
      modulesTemplatesDir: path.join(defaultTemplatesDir, 'modules'),
      ...config,
    };

    this.dependencyChecker = new DependencyChecker();

    logger.debug('TemplateRegistry 初始化', {
      templateDirs: this.config.templateDirs,
      pathConfig: this.pathResolver.getPathConfig()
    });

    if (this.config.autoScan) {
      this.scanAndRegisterTemplates();
    }

    if (this.config.watchMode) {
      this.setupWatchers();
    }
  }

  /**
   * 扫描并注册所有模板
   */
  async scanAndRegisterTemplates(): Promise<void> {
    logger.info('开始扫描模板文件...');

    for (const templateDir of this.config.templateDirs) {
      if (!fs.existsSync(templateDir)) {
        logger.warn(`模板目录不存在: ${templateDir}`);
        continue;
      }
      await this.scanDirectory(templateDir);
    }

    logger.info(`模板扫描完成，共注册 ${this.templates.size} 个模板`);
    this.logRegistryStats();
  }

  /**
   * 扫描目录
   */
  private async scanDirectory(dir: string): Promise<void> {
    logger.debug(`扫描目录: ${dir}`);
    
    const patterns = this.config.patterns.map((pattern) => path.join(dir, pattern).replace(/\\/g, '/'));
    logger.debug(`扫描模式: ${patterns.join(', ')}`);

    for (const pattern of patterns) {
      const files = await glob(pattern, { ignore: ['**/node_modules/**'] });
      logger.debug(`模式 ${pattern} 找到文件: ${files.length} 个`, { files });

      for (const file of files) {
        try {
          logger.debug(`尝试注册模板文件: ${file}`);
          await this.registerTemplateFromFile(file);
        } catch (error) {
          logger.error(`注册模板文件失败: ${file}`, { error: error instanceof Error ? error.message : String(error) });
        }
      }
    }
  }

  /**
   * 从文件注册模板
   */
  async registerTemplateFromFile(filePath: string): Promise<void> {
    logger.debug(`读取模板文件: ${filePath}`);
    const content = fs.readFileSync(filePath, 'utf-8');
    const parsed = yaml.parse(content);

    logger.debug('解析YAML内容', {
      hasTemplate: !!parsed.template,
      hasTemplates: !!parsed.templates,
      keys: Object.keys(parsed)
    });

    // 支持单个模板文件 (template: {...})
    if (parsed.template) {
      logger.debug('注册单个模板');
      const template = this.parseTemplateDefinition(parsed.template, filePath);
      this.registerTemplate(template);
    }

    // 支持多模板文件 (templates: { templateId: {...}, ... })
    if (parsed.templates) {
      logger.debug(`注册多个模板: ${Object.keys(parsed.templates).join(', ')}`);
      for (const [templateId, templateData] of Object.entries(parsed.templates)) {
        const template = this.parseTemplateDefinition(templateData, filePath, templateId);
        this.registerTemplate(template);
      }
    }

    if (!parsed.template && !parsed.templates) {
      logger.warn(`文件 ${filePath} 不包含有效的模板定义`);
    }
  }

  /**
   * 解析模板定义
   */
  private parseTemplateDefinition(
    templateData: any,
    filePath: string,
    templateId?: string,
  ): TemplateDefinition {
    const relativePath = this.pathResolver.relative(filePath);
    const pathParts = relativePath.split(path.sep);

    // 从文件路径推断模块名
    const module = this.extractModuleFromPath(pathParts);
    
    // 直接使用YAML中的type字段，如果没有指定则默认为business
    const templateType: 'shared' | 'business' = templateData.type === 'shared' ? 'shared' : 'business';

    const template: TemplateDefinition = {
      id: templateId || templateData.id || this.generateTemplateId(filePath, templateData.name),
      name: templateData.name || templateId || 'Unnamed Template',
      type: templateType,
      description: templateData.description,
      module,
      parameters: this.parseParameters(templateData.parameters || []),
      steps: templateData.steps || [],
      metadata: {
        ...templateData.metadata,
        filePath: relativePath,
        lastModified: fs.statSync(filePath).mtime.toISOString(),
      },
    };

    return template;
  }

  /**
   * 从路径提取模块名
   */
  private extractModuleFromPath(pathParts: string[]): string | undefined {
    // 如果在 templates 目录下，使用第一级子目录作为模块
    const templatesIndex = pathParts.findIndex((part) => part.includes('template'));
    if (templatesIndex >= 0 && templatesIndex < pathParts.length - 2) {
      return pathParts[templatesIndex + 1];
    }

    return undefined;
  }

  /**
   * 解析参数定义
   */
  private parseParameters(parameters: any[]): TemplateParameter[] {
    return parameters.map((param) => ({
      name: param.name,
      type: param.type || 'string',
      required: param.required !== false,
      description: param.description,
      default: param.default,
      validation: param.validation,
    }));
  }

  /**
   * 生成模板ID
   */
  private generateTemplateId(filePath: string, name?: string): string {
    const fileName = path.basename(filePath, path.extname(filePath));
    const pathParts = path.relative(process.cwd(), filePath).split(path.sep);

    // 使用模块名 + 文件名 + 模板名生成唯一ID
    const module = this.extractModuleFromPath(pathParts);
    const parts = [module, fileName, name].filter(Boolean);

    return parts
      .join('-')
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-');
  }

  /**
   * 注册模板
   */
  registerTemplate(template: TemplateDefinition): void {
    // 检查ID冲突
    if (this.templates.has(template.id)) {
      logger.warn(`模板ID冲突，覆盖现有模板: ${template.id}`);
    }

    // 验证新模板是否会引入循环依赖
    const existingTemplates = Array.from(this.templates.values());
    const validation = this.dependencyChecker.validateNewTemplate(template, existingTemplates);

    if (!validation.isValid) {
      const errors: string[] = [];

      if (validation.cycles.length > 0) {
        errors.push(`检测到循环依赖: ${validation.cycles.map(cycle => cycle.join(' → ')).join(', ')}`);
      }

      if (validation.missingDependencies.length > 0) {
        errors.push(`缺少依赖模板: ${validation.missingDependencies.join(', ')}`);
      }

      throw new Error(`模板注册失败: ${errors.join('; ')}`);
    }

    this.templates.set(template.id, template);

    // 按模块分组
    if (template.module) {
      if (!this.moduleTemplates.has(template.module)) {
        this.moduleTemplates.set(template.module, []);
      }
      this.moduleTemplates.get(template.module)!.push(template);
    }

    logger.debug(`注册模板: ${template.id}`, {
      module: template.module,
      type: template.type,
    });
  }

  /**
   * 获取模板
   */
  getTemplate(id: string): TemplateDefinition | undefined {
    return this.templates.get(id);
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): TemplateDefinition[] {
    return Array.from(this.templates.values());
  }

  /**
   * 按模块获取模板
   */
  getTemplatesByModule(module: string): TemplateDefinition[] {
    return this.moduleTemplates.get(module) || [];
  }

  /**
   * 按类型获取模板
   */
  getTemplatesByType(type: string): TemplateDefinition[] {
    return this.getAllTemplates().filter((template) => template.type === type);
  }


  /**
   * 获取共享模板（通用组件）
   */
  getSharedTemplates(): TemplateDefinition[] {
    return this.getTemplatesByType('shared');
  }

  /**
   * 获取业务模板（业务组件）
   */
  getBusinessTemplates(): TemplateDefinition[] {
    return this.getTemplatesByType('business');
  }

  /**
   * 搜索模板
   */
  searchTemplates(query: {
    name?: string;
    module?: string;
    type?: string;
    tags?: string[];
  }): TemplateDefinition[] {
    return this.getAllTemplates().filter((template) => {
      if (query.name && !template.name.toLowerCase().includes(query.name.toLowerCase())) {
        return false;
      }
      if (query.module && template.module !== query.module) {
        return false;
      }
      if (query.type && template.type !== query.type) {
        return false;
      }
      if (query.tags && query.tags.length > 0) {
        const templateTags = template.metadata?.tags || [];
        if (!query.tags.some((tag) => templateTags.includes(tag))) {
          return false;
        }
      }
      return true;
    });
  }

  /**
   * 获取模板依赖
   */
  getTemplateDependencies(templateId: string): TemplateDefinition[] {
    const template = this.getTemplate(templateId);
    if (!template || !template.metadata?.dependencies) {
      return [];
    }

    return template.metadata.dependencies
      .map((depId) => this.getTemplate(depId))
      .filter(Boolean) as TemplateDefinition[];
  }

  /**
   * 验证模板参数
   */
  validateTemplateParameters(
    templateId: string,
    parameters: Record<string, any>,
  ): {
    isValid: boolean;
    errors: string[];
  } {
    const template = this.getTemplate(templateId);
    if (!template) {
      return { isValid: false, errors: [`模板不存在: ${templateId}`] };
    }

    const errors: string[] = [];

    for (const param of template.parameters || []) {
      const value = parameters[param.name];

      // 检查必需参数
      if (param.required && (value === undefined || value === null)) {
        errors.push(`缺少必需参数: ${param.name}`);
        continue;
      }

      // 类型检查
      if (value !== undefined && !this.validateParameterType(value, param.type)) {
        errors.push(`参数 ${param.name} 类型错误，期望: ${param.type}`);
      }

      // 验证规则检查
      if (value !== undefined && param.validation) {
        const validationErrors = this.validateParameterValue(value, param.validation, param.name);
        errors.push(...validationErrors);
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证参数类型
   */
  private validateParameterType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * 验证参数值
   */
  private validateParameterValue(
    value: any,
    validation: TemplateParameter['validation'],
    paramName: string,
  ): string[] {
    const errors: string[] = [];

    if (!validation) return errors;

    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        errors.push(`参数 ${paramName} 不匹配模式: ${validation.pattern}`);
      }
    }

    if (validation.min !== undefined && typeof value === 'number') {
      if (value < validation.min) {
        errors.push(`参数 ${paramName} 小于最小值: ${validation.min}`);
      }
    }

    if (validation.max !== undefined && typeof value === 'number') {
      if (value > validation.max) {
        errors.push(`参数 ${paramName} 大于最大值: ${validation.max}`);
      }
    }

    if (validation.enum && !validation.enum.includes(value)) {
      errors.push(`参数 ${paramName} 不在允许的值范围内: ${validation.enum.join(', ')}`);
    }

    return errors;
  }

  /**
   * 设置文件监听
   */
  private setupWatchers(): void {
    for (const templateDir of this.config.templateDirs) {
      if (fs.existsSync(templateDir)) {
        const watcher = fs.watch(templateDir, { recursive: true }, (_eventType, filename) => {
          if (filename && (filename.endsWith('.yml') || filename.endsWith('.yaml'))) {
            logger.info(`模板文件变更: ${filename}, 重新扫描...`);
            this.scanAndRegisterTemplates();
          }
        });
        this.watchers.push(watcher);
      }
    }
  }

  /**
   * 输出注册统计信息
   */
  private logRegistryStats(): void {
    const stats = {
      total: this.templates.size,
      byType: {} as Record<string, number>,
      byModule: {} as Record<string, number>,
    };

    for (const template of this.templates.values()) {
      stats.byType[template.type] = (stats.byType[template.type] || 0) + 1;

      if (template.module) {
        stats.byModule[template.module] = (stats.byModule[template.module] || 0) + 1;
      }
    }

    logger.info('模板注册统计:', stats);
  }

  /**
   * 获取依赖检测器
   */
  getDependencyChecker(): DependencyChecker {
    return this.dependencyChecker;
  }

  /**
   * 检查所有模板的循环依赖
   */
  checkAllDependencies(): {
    hasCircularDependency: boolean;
    cycles: string[][];
    stats: any;
  } {
    const allTemplates = Array.from(this.templates.values());
    const result = this.dependencyChecker.checkCircularDependencies(allTemplates);
    const stats = this.dependencyChecker.getDependencyStats();

    return {
      hasCircularDependency: result.hasCircularDependency,
      cycles: result.cycles,
      stats
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    for (const watcher of this.watchers) {
      watcher.close();
    }
    this.watchers = [];
  }
}
