import { logger } from '../utils/logger';
import { getNestedProperty } from '../utils/helpers';
import type { DataSourceConfig } from '../types';

/**
 * 数据源管理器
 * 负责从各种数据源获取数据并进行变量解析
 */
export class DataSourceManager {
  /**
   * 获取单个数据源的数据
   */
  async fetchData(
    dataSource: DataSourceConfig, 
    variables: Record<string, any>
  ): Promise<{ name: string, data: any }> {
    try {
      logger.info(`开始获取数据源: ${dataSource.name}`, { 
        type: dataSource.type,
        url: dataSource.config.url 
      });

      let data: any;

      switch (dataSource.type) {
        case 'api':
          data = await this.fetchFromApi(dataSource, variables);
          break;
        case 'mock':
          data = await this.fetchFromMock(dataSource, variables);
          break;
        default:
          throw new Error(`不支持的数据源类型: ${dataSource.type}。支持的类型: api, mock`);
      }

      logger.info(`数据源 ${dataSource.name} 获取成功`, { 
        dataType: Array.isArray(data) ? 'array' : typeof data,
        dataCount: Array.isArray(data) ? data.length : 'N/A'
      });

      return { name: dataSource.name, data };

    } catch (error) {
      logger.error(`数据源 ${dataSource.name} 获取失败`, { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new Error(`数据源 ${dataSource.name} 获取失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 批量获取多个数据源的数据
   */
  async fetchMultipleData(
    dataSources: DataSourceConfig[], 
    variables: Record<string, any>
  ): Promise<Record<string, any>> {
    if (!dataSources || dataSources.length === 0) {
      return {};
    }

    logger.info(`开始批量获取 ${dataSources.length} 个数据源`);

    try {
      // 并行请求所有数据源
      const promises = dataSources.map(ds => this.fetchData(ds, variables));
      const responses = await Promise.all(promises);

      // 合并结果
      const results: Record<string, any> = {};
      responses.forEach(({ name, data }) => {
        results[name] = data;
      });

      logger.info('批量数据源获取完成', { 
        dataSourceCount: dataSources.length,
        dataSourceNames: Object.keys(results)
      });

      return results;

    } catch (error) {
      logger.error('批量数据源获取失败', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * 从 API 获取数据
   */
  private async fetchFromApi(
    dataSource: DataSourceConfig,
    variables: Record<string, any>
  ): Promise<any> {
    if (!dataSource.config.url) {
      throw new Error(`API数据源 ${dataSource.name} 缺少url配置`);
    }

    const resolvedUrl = this.resolveVariables(dataSource.config.url, variables);
    const method = dataSource.config.method || 'GET';
    const headers = dataSource.config.headers || {};
    const timeout = dataSource.config.timeout || 30000;

    logger.debug('API 请求', { 
      url: resolvedUrl, 
      method, 
      headers: Object.keys(headers) 
    });

    // 创建 AbortController 用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(resolvedUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: dataSource.config.body ? JSON.stringify(dataSource.config.body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`请求超时 (${timeout}ms)`);
      }
      
      throw error;
    }
  }

  /**
   * 从模拟数据获取数据
   */
  private async fetchFromMock(
    dataSource: DataSourceConfig,
    _variables: Record<string, any>
  ): Promise<any> {
    // 返回配置中的模拟数据
    return dataSource.config.data || [];
  }

  /**
   * 解析字符串中的变量引用
   */
  private resolveVariables(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{\s*([^}]+)\s*\}\}/g, (match, key) => {
      const trimmedKey = key.trim();
      
      // 支持嵌套属性访问，如 {{ user.name }}
      const value = getNestedProperty(variables, trimmedKey);
      
      if (value !== undefined && value !== null) {
        return String(value);
      }
      
      logger.warn(`变量 ${trimmedKey} 未找到，保持原样`, { availableKeys: Object.keys(variables) });
      return match;
    });
  }


}
