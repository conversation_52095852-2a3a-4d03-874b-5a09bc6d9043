import { randomUUID } from 'node:crypto';
import { logger } from '../utils/logger';
import type { TestRunResult } from '../runner/test-reporter';
import type { ExecutorType } from '../types';

/**
 * 任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * 测试任务
 */
export interface TestTask {
  id: string;
  yamlContent: string;
  pageId?: string;
  status: TaskStatus;
  startTime: number;
  endTime?: number;
  duration?: number;
  result?: TestRunResult;
  abortController: AbortController;
  executorType?: ExecutorType;
}

/**
 * 测试进度
 */
export interface TestProgress {
  taskId: string;
  status: TaskStatus;
  currentStep: number;
  totalSteps: number;
  stepName?: string;
  progress: number;
  logs: string[];
  screenshots: string[];
  startTime: number;
  elapsedTime: number;
}

/**
 * 纯任务管理器
 * 只负责任务生命周期管理，不包含执行逻辑
 */
export class TaskManager {
  private tasks: Map<string, TestTask> = new Map();

  constructor() {
    logger.debug('TaskManager 初始化完成');
  }

  /**
   * 创建新任务
   */
  createTask(yamlContent: string, pageId?: string, executorType?: ExecutorType): TestTask {
    const task: TestTask = {
      id: randomUUID(),
      yamlContent,
      pageId,
      status: 'pending',
      startTime: Date.now(),
      abortController: new AbortController(),
      executorType,
    };

    this.tasks.set(task.id, task);
    logger.debug('创建新任务', { taskId: task.id, pageId, executorType });

    return task;
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(taskId: string, status: TaskStatus): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      logger.warn('尝试更新不存在的任务状态', { taskId, status });
      return;
    }

    const oldStatus = task.status;
    task.status = status;

    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      task.endTime = Date.now();
      task.duration = task.endTime - task.startTime;
    }

    logger.debug('任务状态更新', { taskId, oldStatus, newStatus: status });
  }

  /**
   * 设置任务结果
   */
  setTaskResult(taskId: string, result: TestRunResult): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      logger.warn('尝试设置不存在任务的结果', { taskId });
      return;
    }

    task.result = result;
    logger.debug('任务结果已设置', { taskId, success: result.success });
  }

  /**
   * 获取任务
   */
  getTask(taskId: string): TestTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取测试结果
   */
  getTestResult(taskId: string): TestRunResult | null {
    const task = this.tasks.get(taskId);
    return task?.result || null;
  }

  /**
   * 取消测试
   */
  async cancelTest(taskId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task) {
      logger.warn('尝试取消不存在的测试任务', { taskId });
      return false;
    }

    if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
      logger.warn('尝试取消已完成的测试任务', { taskId, status: task.status });
      return false;
    }

    logger.info('取消测试任务', { taskId });

    // 取消任务
    task.abortController.abort();
    this.updateTaskStatus(taskId, 'cancelled');

    return true;
  }

  /**
   * 获取运行中的测试
   */
  getRunningTests(): TestTask[] {
    return Array.from(this.tasks.values()).filter(
      task => task.status === 'running' || task.status === 'pending'
    );
  }

  /**
   * 获取测试进度（简化实现）
   */
  getTestProgress(taskId: string): TestProgress | null {
    const task = this.tasks.get(taskId);
    if (!task) return null;

    const now = Date.now();
    return {
      taskId: task.id,
      status: task.status,
      currentStep: 0,
      totalSteps: 0,
      stepName: undefined,
      progress: task.status === 'completed' ? 100 : task.status === 'running' ? 50 : 0,
      logs: [],
      screenshots: [],
      startTime: task.startTime,
      elapsedTime: now - task.startTime,
    };
  }

  /**
   * 获取测试历史（简化实现）
   */
  getTestHistory(_limit: number = 100): TestTask[] {
    return [];
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompletedTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled')) {
      this.tasks.delete(taskId);
      logger.debug('清理已完成任务', { taskId, status: task.status });
    }
  }

  /**
   * 清理所有任务
   */
  cleanup(): void {
    this.tasks.clear();
    logger.debug('TaskManager 清理完成');
  }

  /**
   * 获取任务统计
   */
  getTaskStats(): {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  } {
    const tasks = Array.from(this.tasks.values());
    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === 'pending').length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length,
    };
  }
}
