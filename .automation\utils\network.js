/**
 * 网络监听工具
 * 提供 XHR/Fetch 请求监听功能，避免多线程监听混乱
 */

// 全局监听器管理器，避免多线程冲突
const listenerManager = {
  activeListeners: new Map(), // page -> { responseHandler, requestHandler }

  /**
   * 开始监听网络请求
   * @param {Page} page - Playwright Page 对象
   * @param {Object} options - 监听选项
   * @returns {string} listenerId - 监听器ID
   */
  startListening(page, options = {}) {
    const listenerId = `listener_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const { filterUrl, timeout = 10000 } = options;

    // 如果页面已有监听器，先清理
    if (this.activeListeners.has(page)) {
      this.stopListening(page);
    }

    const capturedRequests = [];
    const capturedResponses = [];

    // 响应监听器
    const responseHandler = async (response) => {
      try {
        const url = response.url();
        const status = response.status();

        // URL 过滤
        if (filterUrl && !url.includes(filterUrl)) {
          return;
        }

        // 只监听 XHR/Fetch 请求
        const resourceType = response.request().resourceType();
        if (!['xhr', 'fetch'].includes(resourceType)) {
          return;
        }

        let responseBody = null;
        try {
          responseBody = await response.text();
        } catch (e) {
          responseBody = `[无法读取响应体: ${e.message}]`;
        }

        capturedResponses.push({
          url,
          status,
          method: response.request().method(),
          headers: await response.allHeaders(),
          body: responseBody,
          timestamp: Date.now(),
          resourceType
        });

      } catch (error) {
        console.warn('响应监听器错误:', error.message);
      }
    };

    // 请求监听器
    const requestHandler = (request) => {
      try {
        const url = request.url();
        const resourceType = request.resourceType();

        // URL 过滤
        if (filterUrl && !url.includes(filterUrl)) {
          return;
        }

        // 只监听 XHR/Fetch 请求
        if (!['xhr', 'fetch'].includes(resourceType)) {
          return;
        }

        capturedRequests.push({
          url,
          method: request.method(),
          headers: request.headers(),
          postData: request.postData(),
          timestamp: Date.now(),
          resourceType
        });

      } catch (error) {
        console.warn('请求监听器错误:', error.message);
      }
    };

    // 注册监听器
    page.on('response', responseHandler);
    page.on('request', requestHandler);

    // 存储监听器信息
    this.activeListeners.set(page, {
      listenerId,
      responseHandler,
      requestHandler,
      capturedRequests,
      capturedResponses,
      startTime: Date.now(),
      timeout
    });

    return listenerId;
  },

  /**
   * 停止监听网络请求
   * @param {Page} page - Playwright Page 对象
   * @returns {Object} 捕获的网络数据
   */
  stopListening(page) {
    const listenerInfo = this.activeListeners.get(page);
    if (!listenerInfo) {
      return { requests: [], responses: [] };
    }

    const { responseHandler, requestHandler, capturedRequests, capturedResponses } = listenerInfo;

    // 移除监听器
    try {
      page.off('response', responseHandler);
      page.off('request', requestHandler);
    } catch (error) {
      console.warn('移除监听器时出错:', error.message);
    }

    // 清理记录
    this.activeListeners.delete(page);

    return {
      requests: [...capturedRequests],
      responses: [...capturedResponses],
      duration: Date.now() - listenerInfo.startTime
    };
  },

  /**
   * 获取当前捕获的网络数据（不停止监听）
   * @param {Page} page - Playwright Page 对象
   * @returns {Object} 当前捕获的网络数据
   */
  getCurrentCapture(page) {
    const listenerInfo = this.activeListeners.get(page);
    if (!listenerInfo) {
      return { requests: [], responses: [] };
    }

    return {
      requests: [...listenerInfo.capturedRequests],
      responses: [...listenerInfo.capturedResponses],
      duration: Date.now() - listenerInfo.startTime
    };
  },

  /**
   * 检查是否有活跃的监听器
   * @param {Page} page - Playwright Page 对象
   * @returns {boolean}
   */
  hasActiveListener(page) {
    return this.activeListeners.has(page);
  }
};

/**
 * 带网络监听的操作执行器
 * @param {Page} page - Playwright Page 对象
 * @param {Function} operation - 要执行的操作函数
 * @param {Object} options - 选项
 * @returns {Object} 操作结果和网络数据
 */
export async function executeWithNetworkMonitoring(page, operation, options = {}) {
  const {
    filterUrl,
    timeout = 10000,
    expectSuccess = true,
    successIndicator = null, // 成功指示器函数
    networkWaitTimeout = 2000, // 等待网络请求的最大时间
    networkCheckInterval = 100 // 检查网络请求的间隔
  } = options;

  // 开始监听
  const listenerId = listenerManager.startListening(page, { filterUrl, timeout });

  try {
    // 执行操作
    const operationResult = await operation();

    // 智能等待网络请求
    await waitForNetworkRequests(page, { networkWaitTimeout, networkCheckInterval });

    // 获取网络数据
    const networkData = listenerManager.stopListening(page);

    // 检查操作是否成功
    let isSuccess = expectSuccess;
    if (successIndicator && typeof successIndicator === 'function') {
      try {
        isSuccess = await successIndicator(page);
      } catch (error) {
        isSuccess = false;
      }
    }

    return {
      success: isSuccess,
      operation: operationResult,
      network: networkData,
      listenerId
    };

  } catch (error) {
    // 确保停止监听
    const networkData = listenerManager.stopListening(page);

    throw {
      success: false,
      error: error.message,
      network: networkData,
      listenerId
    };
  }
}

/**
 * 智能等待网络请求
 * 如果有网络请求则等待完成，如果没有则快速返回
 * @param {Page} page - Playwright Page 对象
 * @param {Object} options - 等待选项
 */
async function waitForNetworkRequests(page, options = {}) {
  const { networkWaitTimeout = 2000, networkCheckInterval = 100 } = options;

  const startTime = Date.now();
  let hasNetworkActivity = false;
  let lastNetworkActivity = startTime;

  while (Date.now() - startTime < networkWaitTimeout) {
    // 检查是否有网络活动
    const currentCapture = listenerManager.getCurrentCapture(page);
    const currentRequestCount = currentCapture.requests.length;
    const currentResponseCount = currentCapture.responses.length;

    // 如果有新的网络活动
    if (currentRequestCount > 0 || currentResponseCount > 0) {
      hasNetworkActivity = true;
      lastNetworkActivity = Date.now();
    }

    // 如果有网络活动，但最近500ms没有新活动，认为网络请求已完成
    if (hasNetworkActivity && Date.now() - lastNetworkActivity > 500) {
      break;
    }

    // 如果没有网络活动且已等待500ms，认为没有网络请求
    if (!hasNetworkActivity && Date.now() - startTime > 500) {
      break;
    }

    await page.waitForTimeout(networkCheckInterval);
  }
}

export { listenerManager };