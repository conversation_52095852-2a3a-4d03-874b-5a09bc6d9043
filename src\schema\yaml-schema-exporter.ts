/**
 * YAML Schema 导出器
 * 负责构建和导出标准的 JSON Schema 定义
 */

import { ScriptLoader } from '../script/script-loader';
import { PathResolver } from '../utils/path-resolver';
import type { ScriptDefinition } from '../script/types';

export class YAMLSchemaExporter {
  
  /**
   * 导出标准 JSON Schema
   */
  static exportJSONSchema(): any {
    return {
      $schema: 'http://json-schema.org/draft-07/schema#',
      title: 'YAML Test DSL Schema',
      type: 'object',
      definitions: {
        step: this.getStepSchema()
      },
      anyOf: [
        // 测试文件结构
        {
          type: 'object',
          required: ['config'],
          properties: {
            config: this.getConfigSchema(),
            hooks: this.getHooksSchema(),
            tests: this.getTestsSchema(),
            templates: this.getTemplatesSchema()
          }
        },
        // 模板文件结构
        {
          type: 'object',
          required: ['templates'],
          properties: {
            templates: this.getTemplatesSchema()
          }
        }
      ]
    };
  }

  /**
   * 为 LLM 导出优化格式 - 简洁的示例驱动格式（同步版本，使用默认schema）
   */
  static exportForLLM(): any {
    return {
      structure: this.getFileStructureExamples(),
      actions: this.getActionExamples(),
      templates: this.getTemplateExamples(),
      config: this.getConfigOptions()
    };
  }

  /**
   * 为 LLM 导出优化格式 - 异步版本，包含动态加载的脚本信息
   */
  static async exportForLLMAsync(scriptDirs?: string[]): Promise<any> {
    try {
      // 获取脚本目录
      const dirsToScan = scriptDirs || [PathResolver.getInstance().getScriptsDir()];

      // 加载脚本
      const loadResult = await ScriptLoader.loadScriptsStatic(dirsToScan);

      // 生成动态的 actions，包含实际可用的脚本信息
      const actions = this.getActionExamplesWithScripts(loadResult.scripts);

      return {
        structure: this.getFileStructureExamples(),
        actions,
        templates: this.getTemplateExamples(),
        config: this.getConfigSchema(),
        availableScripts: this.getAvailableScriptsInfo(loadResult.scripts)
      };
    } catch (error) {
      // 如果加载失败，返回默认的 schema
      console.warn('动态加载脚本失败，使用默认schema:', error);
      return this.exportForLLM();
    }
  }

  /**
   * 获取文件结构示例
   */
  private static getFileStructureExamples(): any {
    return {
      testFile: {
        description: '测试文件基本结构',
        example: `config:
  name: "测试名称"
  description: "测试描述"
  baseUrl: "https://example.com"
  executorType: "element-plus"
  testMode: "flow"  # flow | boundary
  timeout: 30000

tests:
  - name: "测试用例名称"
    description: "测试用例描述"
    steps:
      - action: navigate
        url: "/login"
      - action: fill
        role: textbox
        roleOptions:
          name: "Username"
        value: "testuser"`
      },
      templateFile: {
        description: '模板文件基本结构',
        example: `templates:
  login-template:
    name: "登录模板"
    type: "shared"
    parameters:
      - name: username
        type: "string"
        required: true
      - name: password
        type: "string"
        required: true
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "Username"
        data: "{{username}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "Password"
        data: "{{password}}"`
      }
    };
  }

  /**
   * 获取 Action 示例 - 按功能分类（包含动态脚本信息）
   */
  private static getActionExamplesWithScripts(scripts: ScriptDefinition[]): any {
    const baseActions = this.getActionExamples();

    // 如果有可用脚本，更新 useScript 示例
    if (scripts.length > 0) {
      const scriptExamples = this.generateScriptExamples(scripts);
      baseActions.control.examples.useScript = scriptExamples;
    }

    return baseActions;
  }

  /**
   * 获取 Action 示例 - 按功能分类
   */
  private static getActionExamples(): any {
    return {
      navigation: {
        description: '页面导航操作',
        examples: {
          navigate: `- action: navigate
  url: "https://example.com/login"`,
          goBack: `- action: goBack`,
          reload: `- action: reload`
        }
      },
      interaction: {
        description: '元素交互操作',
        examples: {
          click: `- action: click
  role: button
  roleOptions:
    name: "Submit"`,
          fill: `- action: fill
  role: textbox
  roleOptions:
    name: "Username"
  value: "testuser"`,
          fillWithContext: `- action: fill
  role: textbox
  roleOptions:
    name: "名称"
  within:
    role: dialog
    roleOptions:
      name: "新增车辆"
  value: "测试车辆"`,
          selectOption: `- action: selectOption
  role: combobox
  roleOptions:
    name: "Country"
  value: "China"`,
          selectWithContext: `- action: selectOption
  role: combobox
  roleOptions:
    name: "型号"
  within:
    selector: '[role="dialog"]'
  value: "XH001"`,
          selectDate: `- action: selectDate
  role: combobox
  roleOptions:
    name: "Date"
  value: "2021-01-01"`,
          check: `- action: check
  role: checkbox
  roleOptions:
    name: "Remember me"`
        }
      },
      wait: {
        description: '等待操作',
        examples: {
          wait: `- action: wait
  data: 2000
  when: "{{pageTitle}} === 'Welcome'"
  `,
          waitForSelector: `- action: waitForSelector
  selector: ".loading"
  state: "hidden"`,
          waitForText: `- action: waitForText
  data: "Welcome"`
        }
      },
      verification: {
        description: '验证操作',
        examples: {
          verify: `- action: verify
  type: element
  role: alert
  roleOptions:
    name: "Success"
  assertion: visible`
        }
      },
      extraction: {
        description: '数据提取操作',
        examples: {
          extractText: `- action: extractText
  selector: "h1"
  variable: "pageTitle"`
        }
      },
      setVariable: {
        description: '设置变量操作',
        examples: {
          setVariable: `- action: setVariable
  name: "pageTitle"
  value: "{{pageTitle}}"`
        }
      },
      control: {
        description: '控制流操作',
        examples: {
          forEach: `- action: forEach
  items: "{{testData}}"
  steps:
    - action: fill
      role: textbox
      value: "{{item.name}}"`,
          useTemplate: `- action: useTemplate
  template: "login-template"
  parameters:
    username: "testuser"
    password: "password123"`,
          useScript: `- action: useScript
  script: "complex-login"
  parameters:
    username: "{{user.name}}"
    password: "{{user.password}}"
    timeout: 30000
    retryCount: 3
  returnAs: "loginResult"`
        }
      }
    };
  }

  /**
   * 获取模板示例
   */
  private static getTemplateExamples(): any {
    return {
      basic: {
        description: '基础模板结构',
        example: `templates:
  form-fill-template:
    name: "表单填写模板"
    type: "shared"
    parameters:
      - name: formData
        type: "object"
        required: true
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "Name"
        data: "{{formData.name}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "Email"
        data: "{{formData.email}}"`
      },
      usage: {
        description: '模板使用示例',
        example: `tests:
  - name: "使用模板的测试"
    description: "通过模板进行表单填写测试"
    useTemplate: "form-fill-template"
    parameters:
      formData:
        name: "John Doe"
        email: "<EMAIL>"`
      }
    };
  }

  /**
   * 获取配置选项
   */
  private static getConfigOptions(): any {
    return {
      basic: {
        description: '基础配置选项',
        options: {
          name: '测试套件名称',
          baseUrl: '基础URL',
          testMode: 'flow (流程测试) | boundary (边界测试)',
          executorType: 'web (默认) | element-plus'
        }
      },
      advanced: {
        description: '高级配置选项',
        options: {
          timeout: '默认超时时间(毫秒)',
          retries: '重试次数',
          parallel: '是否并行执行',
          captureScreenshots: '是否截图',
          continueOnFailure: '失败后是否继续'
        }
      },
      variables: {
        description: '变量配置',
        options: {
          variables: {
            type: 'object',
            description: '变量配置',
          }
        },
      },
      dataSources: {
        description: '数据源配置',
        example: `dataSources:
  - name: "formData"
    type: "api"
    config:
      url: "http://localhost:3000/api/test-data"
      method: "GET"`
      },
      hooks: {
        description: '钩子函数（顶级属性，与config平级）',
        example: `config:
  name: "测试配置"
  testMode: "flow"

hooks:
  beforeEach:
    - action: navigate
      url: "/login"
  afterEach:
    - action: screenshot

tests:
  - name: "测试用例"
    steps:
      - action: wait
        data: 100`
      }
    };
  }

  /**
   * 获取配置 Schema
   */
  private static getConfigSchema(): any {
    return {
      type: 'object',
      required: ['name'],
      additionalProperties: false,
      properties: {
        name: { type: 'string', description: '测试套件名称' },
        description: { type: 'string', description: '测试套件描述' },
        testMode: {
          type: 'string',
          enum: ['flow', 'boundary'],
          description: '测试模式，flow为流程测试，boundary为边界测试'
        },

        baseUrl: { type: 'string', description: '基础URL' },
        timeout: { type: 'number', description: '超时时间(毫秒)' },
        retries: { type: 'number', description: '重试次数' },
        parallel: { type: 'boolean', description: '是否并行执行' },
        captureScreenshots: { type: 'boolean', description: '是否截图' },
        continueOnFailure: { type: 'boolean', description: '失败后是否继续' },
        forceCleanupOnFailure: { type: 'boolean', description: '测试失败后是否强制执行清理钩子' },
        variables: { type: 'object', description: '全局变量配置对象, 支持嵌套对象, 例如: { "user": { "name": "testuser", "password": "password123" } }' },
        executorType: {
          type: 'string',
          enum: ['web', 'element-plus'],
          description: '执行器类型，默认为web，element-plus为 vue3 的 element-plus 测试框架'
        },
        dataSources: {
          type: 'array',
          items: {
            type: 'object',
            required: ['name', 'type'],
            properties: {
              name: { type: 'string', description: '数据源名称' },
              type: { type: 'string', enum: ['api', 'mock'], description: '数据源类型，api为API数据源，mock为mock数据源' },
              config: {
                type: 'object',
                properties: {
                  url: { type: 'string', description: 'API地址' },
                  method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'], description: 'API方法' },
                  headers: { type: 'object', description: 'API请求头' },
                  body: { type: 'object', description: 'API请求体' },
                  timeout: { type: 'number', description: 'API超时时间(毫秒)' },
                  data: { type: 'object', description: 'API请求数据' }
                }
              }
            }
          }
        }
      }
    };
  }

  /**
   * 获取钩子函数 Schema
   */
  private static getHooksSchema(): any {
    return {
      type: 'object',
      additionalProperties: false,
      properties: {
        beforeEach: {
          type: 'array',
          items: this.getStepSchema(),
          description: '每个测试用例执行前的钩子函数'
        },
        afterEach: {
          type: 'array',
          items: this.getStepSchema(),
          description: '每个测试用例执行后的钩子函数'
        },
        beforeAll: {
          type: 'array',
          items: this.getStepSchema(),
          description: '所有测试用例执行前的钩子函数'
        },
        afterAll: {
          type: 'array',
          items: this.getStepSchema(),
          description: '所有测试用例执行后的钩子函数'
        }
      }
    };
  }

  /**
   * 获取测试用例 Schema
   */
  private static getTestsSchema(): any {
    return {
      anyOf: [
        // 数组格式
        {
          type: 'array',
          items: this.getTestCaseSchema()
        },
        // 对象格式
        {
          type: 'object',
          patternProperties: {
            '^[a-zA-Z0-9_-]+$': this.getTestCaseSchema()
          }
        }
      ]
    };
  }

  /**
   * 获取单个测试用例 Schema
   */
  private static getTestCaseSchema(): any {
    return {
      type: 'object',
      anyOf: [
        // 普通测试用例
        {
          required: ['name', 'steps'],
          properties: {
            name: { type: 'string' },
            description: { type: 'string' },
            tags: { type: 'array', items: { type: 'string' } },
            steps: { type: 'array', items: this.getStepSchema() },
            data: { type: 'object' },
            config: {
              type: 'object',
              properties: {
                executorType: { type: 'string', enum: ['web', 'element-plus'] },
                timeout: { type: 'number' },
                retries: { type: 'number' }
              }
            }
          }
        },
        // 模板调用测试用例
        {
          required: ['name', 'useTemplate'],
          properties: {
            name: { type: 'string' },
            description: { type: 'string' },
            useTemplate: { type: 'string' },
            parameters: { type: 'object' },
            forEach: { type: 'string' },
            expectError: { type: 'string' },
            saveToContext: { type: 'object' },
            postSteps: { type: 'array', items: this.getStepSchema() }
          }
        }
      ]
    };
  }

  /**
   * 获取步骤 Schema - 使用标准 JSON Schema 格式
   */
  private static getStepSchema(): any {
    const actionSchemas = this.getActionSchemas();
    
    return {
      type: 'object',
      anyOf: Object.values(actionSchemas),
      properties: {
        id: { type: 'string' },
        name: { type: 'string' }
      }
    };
  }

  /**
   * 获取元素定位器的属性定义
   */
  private static getElementLocatorProperties(): any {
    return {
      role: {
        type: 'string',
        enum: ['alert', 'alertdialog', 'application', 'article', 'banner', 'blockquote', 'button', 'caption', 'cell', 'checkbox', 'code', 'columnheader', 'combobox', 'complementary', 'contentinfo', 'definition', 'deletion', 'dialog', 'directory', 'document', 'emphasis', 'feed', 'figure', 'form', 'generic', 'grid', 'gridcell', 'group', 'heading', 'img', 'insertion', 'link', 'list', 'listbox', 'listitem', 'log', 'main', 'marquee', 'math', 'meter', 'menu', 'menubar', 'menuitem', 'menuitemcheckbox', 'menuitemradio', 'navigation', 'none', 'note', 'option', 'paragraph', 'presentation', 'progressbar', 'radio', 'radiogroup', 'region', 'row', 'rowgroup', 'rowheader', 'scrollbar', 'search', 'searchbox', 'separator', 'slider', 'spinbutton', 'status', 'strong', 'subscript', 'superscript', 'switch', 'tab', 'table', 'tablist', 'tabpanel', 'term', 'textbox', 'time', 'timer', 'toolbar', 'tooltip', 'tree', 'treegrid', 'treeitem'],
        description: '元素角色（推荐使用）'
      },
      roleOptions: {
        type: 'object',
        description: 'Role定位选项',
        properties: {
          checked: { type: 'boolean', description: 'aria-checked状态' },
          disabled: { type: 'boolean', description: 'aria-disabled状态' },
          exact: { type: 'boolean', description: '精确匹配名称' },
          expanded: { type: 'boolean', description: 'aria-expanded状态' },
          includeHidden: { type: 'boolean', description: '包含隐藏元素' },
          level: { type: 'number', description: 'aria-level级别' },
          name: {
            oneOf: [
              { type: 'string' },
              { type: 'string', pattern: '^/.+/[gimuy]*$' }
            ],
            description: '可访问名称（字符串或正则表达式）'
          },
          pressed: { type: 'boolean', description: 'aria-pressed状态' },
          selected: { type: 'boolean', description: 'aria-selected状态' }
        }
      },
      selector: { type: 'string', description: 'CSS选择器（备选方案）' },
      within: {
        type: 'object',
        description: '指定元素查找的容器上下文',
        anyOf: [
          { required: ['role'] },
          { required: ['selector'] }
        ],
        properties: {
          role: {
            type: 'string',
            enum: ['alert', 'alertdialog', 'application', 'article', 'banner', 'blockquote', 'button', 'caption', 'cell', 'checkbox', 'code', 'columnheader', 'combobox', 'complementary', 'contentinfo', 'definition', 'deletion', 'dialog', 'directory', 'document', 'emphasis', 'feed', 'figure', 'form', 'generic', 'grid', 'gridcell', 'group', 'heading', 'img', 'insertion', 'link', 'list', 'listbox', 'listitem', 'log', 'main', 'marquee', 'math', 'meter', 'menu', 'menubar', 'menuitem', 'menuitemcheckbox', 'menuitemradio', 'navigation', 'none', 'note', 'option', 'paragraph', 'presentation', 'progressbar', 'radio', 'radiogroup', 'region', 'row', 'rowgroup', 'rowheader', 'scrollbar', 'search', 'searchbox', 'separator', 'slider', 'spinbutton', 'status', 'strong', 'subscript', 'superscript', 'switch', 'tab', 'table', 'tablist', 'tabpanel', 'term', 'textbox', 'time', 'timer', 'toolbar', 'tooltip', 'tree', 'treegrid', 'treeitem'],
            description: '容器元素角色'
          },
          roleOptions: {
            type: 'object',
            description: '容器Role定位选项',
            properties: {
              checked: { type: 'boolean', description: 'aria-checked状态' },
              disabled: { type: 'boolean', description: 'aria-disabled状态' },
              exact: { type: 'boolean', description: '精确匹配名称' },
              expanded: { type: 'boolean', description: 'aria-expanded状态' },
              includeHidden: { type: 'boolean', description: '包含隐藏元素' },
              level: { type: 'number', description: 'aria-level级别' },
              name: {
                oneOf: [
                  { type: 'string' },
                  { type: 'string', pattern: '^/.+/[gimuy]*$' }
                ],
                description: '可访问名称（字符串或正则表达式）'
              },
              pressed: { type: 'boolean', description: 'aria-pressed状态' },
              selected: { type: 'boolean', description: 'aria-selected状态' }
            }
          },
          selector: { type: 'string', description: '容器CSS选择器' }
        },
        additionalProperties: false
      }
    };
  }

  /**
   * 获取元素定位器的验证规则
   */
  private static getElementLocatorValidation(): any {
    return {
      anyOf: [
        { required: ['role'] },
        { required: ['selector'] }
      ]
    };
  }

  /**
   * 为 action schema 添加通用字段
   */
  private static addCommonFields(schema: any): any {
    return {
      ...schema,
      properties: {
        description: { type: 'string', description: '步骤描述' },
        ...schema.properties,
        when: {
          type: 'string',
          description: '条件表达式，当条件为真时才执行此步骤'
        }
      }
    };
  }

  /**
   * 获取所有 Action 的标准 JSON Schema 定义
   */
  private static getActionSchemas(): Record<string, any> {
    const schemas = {
      // Navigation actions
      navigate: {
        type: 'object',
        required: ['action'],
        anyOf: [
          { required: ['url'] },
          { required: ['data'] }
        ],
        properties: {
          action: { const: 'navigate' },
          url: { type: 'string', description: '目标URL' },
          data: { description: '从变量获取的URL' },
          timeout: { type: 'number', description: '超时时间(毫秒)' }
        },
        additionalProperties: false
      },

      goBack: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'goBack' }
        },
        additionalProperties: false
      },

      goForward: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'goForward' }
        },
        additionalProperties: false
      },

      reload: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'reload' }
        },
        additionalProperties: false
      },

      // Element interaction actions
      click: {
        type: 'object',
        required: ['action'],
        allOf: [this.getElementLocatorValidation()],
        properties: {
          action: { const: 'click' },
          ...this.getElementLocatorProperties(),
          timeout: { type: 'number' },
          hover: { type: 'boolean', description: '是否悬停' },
          force: { type: 'boolean', description: '强制点击' }
        },
        additionalProperties: false
      },
      
      hover: {
        type: 'object',
        required: ['action', 'selector'],
        properties: {
          action: { const: 'hover' },
          selector: { type: 'string', description: '元素选择器' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      // Form actions
      fill: {
        type: 'object',
        required: ['action'],
        allOf: [
          this.getElementLocatorValidation(),
          {
            anyOf: [
              { required: ['value'] },
              { required: ['data'] }
            ]
          }
        ],
        properties: {
          action: { const: 'fill' },
          ...this.getElementLocatorProperties(),
          value: { description: '填入的值' },
          data: { description: '从变量获取的数据' },
          clear: { type: 'boolean', description: '填入前是否清空' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      selectOption: {
        type: 'object',
        required: ['action'],
        allOf: [this.getElementLocatorValidation()],
        properties: {
          action: { const: 'selectOption' },
          ...this.getElementLocatorProperties(),
          value: { description: '选择的值' },
          data: { description: '从变量获取的数据' },
          label: { description: '选择的标签文本' },
          index: { description: '选择的索引' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      selectDate: {
        type: 'object',
        required: ['action'],
        allOf: [this.getElementLocatorValidation()],
        properties: {
          action: { const: 'selectDate' },
          ...this.getElementLocatorProperties(),
          value: { description: '选择的日期' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      check: {
        type: 'object',
        required: ['action'],
        allOf: [this.getElementLocatorValidation()],
        properties: {
          ...this.getElementLocatorProperties(),
          action: { const: 'check' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      uncheck: {
        type: 'object',
        required: ['action'],
        properties: {
          ...this.getElementLocatorProperties(),
          action: { const: 'uncheck' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      upload: {
        type: 'object',
        required: ['action'],
        allOf: [this.getElementLocatorValidation()],
        properties: {
          ...this.getElementLocatorProperties(),
          action: { const: 'setInputFiles' },
          files: {
            type: 'array',
            items: { type: 'string' },
            description: '文件路径数组'
          },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      // Wait actions
      wait: {
        type: 'object',
        required: ['action'],
        anyOf: [
          { required: ['data'] },
        ],
        properties: {
          action: { const: 'wait' },
          data: { type: 'number', description: '等待时间(毫秒)' },
        },
        additionalProperties: false
      },

      waitForSelector: {
        type: 'object',
        required: ['action', 'selector'],
        properties: {
          action: { const: 'waitForSelector' },
          selector: { type: 'string', description: '等待的元素选择器' },
          state: {
            type: 'string',
            enum: ['visible', 'hidden', 'attached', 'detached'],
            description: '等待的状态'
          },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      waitForText: {
        type: 'object',
        required: ['action', 'data'],
        properties: {
          action: { const: 'waitForText' },
          data: { type: 'string', description: '等待的文本内容' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      waitForResponse: {
        type: 'object',
        required: ['action', 'data'],
        properties: {
          action: { const: 'waitForResponse' },
          data: { type: 'string', description: '等待的响应URL模式' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      waitForLoadState: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'waitForLoadState' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      // Verification actions
      verify: {
        type: 'object',
        anyOf: [
          {
            // element 类型验证
            required: ['action', 'type'],
            allOf: [this.getElementLocatorValidation()],
            properties: {
              action: { const: 'verify' },
              type: { const: 'element' },
              assertion: {
                type: 'string',
                enum: ['visible', 'hidden', 'enabled', 'disabled', 'checked', 'value', 'text', 'count', 'empty']
              },
              ...this.getElementLocatorProperties(),
              expected: { description: '期望值' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          },
          {
            // text 类型验证
            required: ['action', 'type', 'data'],
            properties: {
              action: { const: 'verify' },
              type: { const: 'text' },
              data: { type: 'string', description: '要验证的文本内容' },
              assertion: {
                type: 'string',
                enum: ['visible', 'hidden', 'equals', 'contains', 'startsWith', 'endsWith', 'matches']
              },
              expected: { description: '期望值' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          },
          {
            // count 类型验证
            required: ['action', 'type', 'selector'],
            properties: {
              action: { const: 'verify' },
              type: { const: 'count' },
              selector: { type: 'string', description: '元素选择器' },
              assertion: {
                type: 'string',
                enum: ['equals', 'greaterThan', 'lessThan']
              },
              expected: { type: 'number', description: '期望的数量' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          },
          {
            // attribute 类型验证
            required: ['action', 'type', 'selector', 'property'],
            properties: {
              action: { const: 'verify' },
              type: { const: 'attribute' },
              selector: { type: 'string', description: '元素选择器' },
              property: { type: 'string', description: '属性名称' },
              assertion: {
                type: 'string',
                enum: ['equals', 'contains', 'startsWith', 'endsWith', 'matches']
              },
              expected: { description: '期望值' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          },
          {
            // page 类型验证
            required: ['action', 'type', 'property'],
            properties: {
              action: { const: 'verify' },
              type: { const: 'page' },
              property: {
                type: 'string',
                enum: ['title', 'url'],
                description: '页面属性(title, url等)'
              },
              assertion: {
                type: 'string',
                enum: ['equals', 'contains', 'startsWith', 'endsWith', 'matches']
              },
              expected: { description: '期望值' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          },
          {
            // class 类型验证
            required: ['action', 'type', 'selector'],
            properties: {
              action: { const: 'verify' },
              type: { const: 'class' },
              selector: { type: 'string', description: '元素选择器' },
              assertion: {
                type: 'string',
                enum: ['contains', 'equals']
              },
              expected: { type: 'string', description: '期望的类名' },
              timeout: { type: 'number' }
            },
            additionalProperties: false
          }
        ]
      },

      // Data extraction actions
      extract: {
        type: 'object',
        required: ['action', 'selector', 'variable'],
        properties: {
          action: { const: 'extract' },
          selector: { type: 'string', description: '元素选择器' },
          variable: { type: 'string', description: '保存到的变量名' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      extractText: {
        type: 'object',
        required: ['action', 'selector', 'variable'],
        properties: {
          action: { const: 'extractText' },
          selector: { type: 'string', description: '元素选择器' },
          variable: { type: 'string', description: '保存到的变量名' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      extractAttribute: {
        type: 'object',
        required: ['action', 'selector', 'attribute', 'variable'],
        properties: {
          action: { const: 'extractAttribute' },
          selector: { type: 'string', description: '元素选择器' },
          attribute: { type: 'string', description: '属性名' },
          variable: { type: 'string', description: '保存到的变量名' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      extractApiResponse: {
        type: 'object',
        required: ['action', 'variable'],
        properties: {
          action: { const: 'extractApiResponse' },
          variable: { type: 'string', description: '保存到的变量名' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },
      
      setVariable: {
        type: 'object',
        required: ['action', 'variable', 'value'],
        properties: {
          action: { const: 'setVariable' },
          name: { type: 'string', description: '保存到的变量名' },
          value: { type: 'string', description: '设置的值' },
        },
        additionalProperties: false
      },

      // Control flow actions
      condition: {
        type: 'object',
        required: ['action', 'if'],
        properties: {
          action: { const: 'condition' },
          if: { type: 'string', description: '条件表达式' },
          then: {
            type: 'array',
            items: { $ref: '#' },
            description: '条件为真时执行的步骤'
          },
          else: {
            type: 'array',
            items: { $ref: '#' },
            description: '条件为假时执行的步骤（可选）'
          }
        },
        additionalProperties: false
      },

      forEach: {
        type: 'object',
        required: ['action', 'items', 'steps'],
        properties: {
          action: { const: 'forEach' },
          items: {
            oneOf: [
              { type: 'array', description: '循环的数组' },
              { type: 'string', description: '变量路径' }
            ],
            description: '循环的数组或变量路径'
          },
          steps: { type: 'array', items: { $ref: '#/definitions/step' } },
          item: { type: 'string', description: '当前项变量名' },
          index: { type: 'string', description: '索引变量名' }
        },
        additionalProperties: false
      },

      forEachElement: {
        type: 'object',
        required: ['action', 'selector', 'steps'],
        properties: {
          action: { const: 'forEachElement' },
          selector: { type: 'string', description: '元素选择器' },
          steps: { type: 'array', items: { $ref: '#/definitions/step' } }
        },
        additionalProperties: false
      },

      // Template actions
      useTemplate: {
        type: 'object',
        required: ['action', 'template'],
        properties: {
          action: { const: 'useTemplate' },
          template: { type: 'string', description: '模板名称' },
          parameters: { type: 'object', description: '模板参数' },
          description: { type: 'string', description: '步骤描述' }
        },
        additionalProperties: false
      },

      // Utility actions
      screenshot: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'screenshot' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      scroll: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'scroll' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      executeScript: {
        type: 'object',
        required: ['action', 'script'],
        properties: {
          action: { const: 'executeScript' },
          script: { type: 'string', description: 'JavaScript 代码' },
          description: { type: 'string', description: '步骤描述' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      useScript: {
        type: 'object',
        required: ['action', 'script'],
        properties: {
          action: { const: 'useScript' },
          script: {
            type: 'string',
            description: '脚本名称，对应scripts目录中的脚本文件'
          },
          parameters: {
            type: 'object',
            description: '传递给脚本的参数对象',
            additionalProperties: true
          },
          returnAs: {
            type: 'string',
            description: '将脚本返回值保存到指定变量名'
          },
          timeout: {
            type: 'number',
            description: '脚本执行超时时间(毫秒)',
            default: 30000,
            minimum: 1000
          },
          description: {
            type: 'string',
            description: '步骤描述'
          }
        },
        additionalProperties: false
      },

      press: {
        type: 'object',
        required: ['action', 'selector', 'key'],
        properties: {
          action: { const: 'press' },
          selector: { type: 'string', description: '元素选择器' },
          key: { type: 'string', description: '按键名称' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      },

      download: {
        type: 'object',
        required: ['action'],
        properties: {
          action: { const: 'download' },
          timeout: { type: 'number' }
        },
        additionalProperties: false
      }
    };

    // 为所有 schema 添加通用字段
    const result: Record<string, any> = {};
    for (const [key, schema] of Object.entries(schemas)) {
      result[key] = this.addCommonFields(schema);
    }

    return result;
  }

  /**
   * 获取模板 Schema
   */
  private static getTemplatesSchema(): any {
    return {
      type: 'object',
      patternProperties: {
        '^[a-zA-Z0-9_-]+$': {
          type: 'object',
          required: ['name', 'steps'],
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            type: {
              type: 'string',
              enum: ['shared', 'business'],
            },
            description: { type: 'string' },
            parameters: {
              type: 'array',
              items: {
                type: 'object',
                required: ['name', 'type'],
                properties: {
                  name: { type: 'string' },
                  type: {
                    type: 'string',
                    enum: ['string', 'number', 'boolean', 'object', 'array']
                  },
                  required: { type: 'boolean' },
                  default: {},
                  description: { type: 'string' },
                  validation: { type: 'object' }
                }
              }
            },
            steps: { type: 'array', items: this.getStepSchema() },
            dependencies: { type: 'array', items: { type: 'string' } },
            metadata: {
              type: 'object',
              properties: {
                author: { type: 'string' },
                version: { type: 'string' },
                tags: { type: 'array', items: { type: 'string' } },
                dependencies: { type: 'array', items: { type: 'string' } }
              }
            }
          }
        }
      }
    };
  }

  /**
   * 生成脚本示例
   */
  private static generateScriptExamples(scripts: ScriptDefinition[]): string {
    if (scripts.length === 0) {
      return `- action: useScript
  script: "example-script"
  parameters:
    param1: "value1"
  returnAs: "result"`;
    }

    // 选择第一个脚本作为示例
    const exampleScript = scripts[0];
    const metadata = exampleScript.metadata;

    let example = `- action: useScript
  script: "${exampleScript.name}"`;

    // 添加参数示例
    if (metadata.parameters && Object.keys(metadata.parameters).length > 0) {
      example += `\n  parameters:`;
      for (const [paramName, paramDef] of Object.entries(metadata.parameters)) {
        const paramInfo = paramDef as any;
        let exampleValue = this.getExampleValueForType(paramInfo.type || 'string');
        example += `\n    ${paramName}: ${JSON.stringify(exampleValue)}`;
      }
    }

    // 添加返回值示例
    example += `\n  returnAs: "scriptResult"`;

    // 添加描述
    if (metadata.description) {
      example += `\n  # ${metadata.description}`;
    }

    return example;
  }

  /**
   * 获取可用脚本信息
   */
  private static getAvailableScriptsInfo(scripts: ScriptDefinition[]): any {
    return {
      count: scripts.length,
      scripts: scripts.map(script => ({
        name: script.name,
        description: script.metadata.description || '',
        parameters: this.formatScriptParameters(script.metadata.parameters || {}),
        returns: script.metadata.returns || {},
      }))
    };
  }

  /**
   * 格式化脚本参数信息
   */
  private static formatScriptParameters(parameters: any): any {
    const formatted: any = {};

    for (const [paramName, paramDef] of Object.entries(parameters)) {
      const paramInfo = paramDef as any;
      formatted[paramName] = {
        type: paramInfo.type || 'string',
        required: paramInfo.required || false,
        description: paramInfo.description || '',
        default: paramInfo.default
      };
    }

    return formatted;
  }

  /**
   * 根据类型获取示例值
   */
  private static getExampleValueForType(type: string): any {
    switch (type) {
      case 'string':
        return 'example-value';
      case 'number':
        return 123;
      case 'boolean':
        return true;
      case 'array':
        return ['item1', 'item2'];
      case 'object':
        return { key: 'value' };
      default:
        return 'example-value';
    }
  }
}
