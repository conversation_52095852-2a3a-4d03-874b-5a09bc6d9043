{"name": "@automation-testing/core", "version": "0.1.0", "description": "Core framework for automation testing", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "biome check src/", "debug": "npx tsx src/demo-test.ts", "start:browser": "npx tsx src/start.ts", "lint:fix": "biome check --apply src/"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@playwright/test": "^1.53.1", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "better-ajv-errors": "^2.0.2", "dotenv": "^16.3.1", "glob": "^11.0.3", "openai": "^4.20.1", "tsx": "^4.6.0", "winston": "^3.17.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/node": "^20.9.0", "typescript": "^5.2.2"}, "peerDependencies": {"typescript": ">=4.5.0"}, "files": ["dist/**/*", "README.md"], "keywords": ["automation", "testing", "playwright", "e2e", "framework"], "repository": {"type": "git", "url": "https://github.com/your-org/automation-testing-framework.git", "directory": "packages/core"}, "license": "MIT"}